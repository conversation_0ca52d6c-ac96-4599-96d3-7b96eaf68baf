#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据标注工具
Data Annotation Tool

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import cv2
import numpy as np
import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from PIL import Image, ImageTk

from ..utils.logger import LoggerMixin


class AnnotationTool(LoggerMixin):
    """数据标注工具"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化标注工具
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.current_image = None
        self.current_image_path = None
        self.current_annotation = {}
        self.image_list = []
        self.current_index = 0
        
        # 字符集定义
        self.provinces = ['京', '津', '沪', '渝', '冀', '豫', '云', '辽', '黑', '湘', '皖', '鲁', 
                         '新', '苏', '浙', '赣', '鄂', '桂', '甘', '晋', '蒙', '陕', '吉', '闽', 
                         '贵', '粤', '青', '藏', '川', '宁', '琼']
        
        self.letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 
                       'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
        
        self.digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
        
        # 车牌颜色
        self.plate_colors = ['blue', 'yellow', 'green', 'white', 'black']
        
        # 初始化GUI
        self._init_gui()
        
        self.logger.info("数据标注工具初始化完成")
    
    def _init_gui(self) -> None:
        """初始化图形用户界面"""
        self.root = tk.Tk()
        self.root.title("车牌数据标注工具")
        self.root.geometry("1200x800")
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧图像显示区域
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 图像显示标签
        self.image_label = ttk.Label(left_frame, text="请选择图像文件夹")
        self.image_label.pack(pady=10)
        
        # 图像导航按钮
        nav_frame = ttk.Frame(left_frame)
        nav_frame.pack(pady=10)
        
        ttk.Button(nav_frame, text="上一张", command=self.prev_image).pack(side=tk.LEFT, padx=5)
        ttk.Button(nav_frame, text="下一张", command=self.next_image).pack(side=tk.LEFT, padx=5)
        
        self.image_info_label = ttk.Label(nav_frame, text="0/0")
        self.image_info_label.pack(side=tk.LEFT, padx=10)
        
        # 右侧标注区域
        right_frame = ttk.Frame(main_frame, width=300)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        right_frame.pack_propagate(False)
        
        # 文件操作按钮
        file_frame = ttk.LabelFrame(right_frame, text="文件操作")
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(file_frame, text="选择文件夹", command=self.select_folder).pack(fill=tk.X, pady=2)
        ttk.Button(file_frame, text="保存标注", command=self.save_annotation).pack(fill=tk.X, pady=2)
        ttk.Button(file_frame, text="加载标注", command=self.load_annotation).pack(fill=tk.X, pady=2)
        
        # 车牌信息标注
        plate_frame = ttk.LabelFrame(right_frame, text="车牌信息")
        plate_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 车牌号码输入
        ttk.Label(plate_frame, text="车牌号码:").pack(anchor=tk.W)
        self.plate_number_var = tk.StringVar()
        self.plate_number_entry = ttk.Entry(plate_frame, textvariable=self.plate_number_var)
        self.plate_number_entry.pack(fill=tk.X, pady=(0, 5))
        self.plate_number_var.trace('w', self.on_plate_number_change)
        
        # 车牌颜色选择
        ttk.Label(plate_frame, text="车牌颜色:").pack(anchor=tk.W)
        self.plate_color_var = tk.StringVar(value="blue")
        color_combo = ttk.Combobox(plate_frame, textvariable=self.plate_color_var, 
                                  values=self.plate_colors, state="readonly")
        color_combo.pack(fill=tk.X, pady=(0, 5))
        
        # 字符详细信息
        char_frame = ttk.LabelFrame(right_frame, text="字符信息")
        char_frame.pack(fill=tk.BOTH, expand=True)
        
        # 字符列表
        self.char_listbox = tk.Listbox(char_frame, height=8)
        self.char_listbox.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        self.char_listbox.bind('<<ListboxSelect>>', self.on_char_select)
        
        # 字符编辑
        char_edit_frame = ttk.Frame(char_frame)
        char_edit_frame.pack(fill=tk.X)
        
        ttk.Label(char_edit_frame, text="字符:").pack(side=tk.LEFT)
        self.char_var = tk.StringVar()
        self.char_entry = ttk.Entry(char_edit_frame, textvariable=self.char_var, width=5)
        self.char_entry.pack(side=tk.LEFT, padx=(5, 0))
        
        ttk.Button(char_edit_frame, text="更新", command=self.update_char).pack(side=tk.LEFT, padx=(5, 0))
        
        # 快捷键绑定
        self.root.bind('<Left>', lambda e: self.prev_image())
        self.root.bind('<Right>', lambda e: self.next_image())
        self.root.bind('<Control-s>', lambda e: self.save_annotation())
    
    def run(self) -> None:
        """运行标注工具"""
        self.root.mainloop()
    
    def select_folder(self) -> None:
        """选择图像文件夹"""
        folder_path = filedialog.askdirectory(title="选择图像文件夹")
        if folder_path:
            self.load_images_from_folder(folder_path)
    
    def load_images_from_folder(self, folder_path: str) -> None:
        """
        从文件夹加载图像
        
        Args:
            folder_path (str): 文件夹路径
        """
        folder = Path(folder_path)
        
        # 支持的图像格式
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        
        self.image_list = []
        for ext in image_extensions:
            self.image_list.extend(list(folder.glob(f'*{ext}')))
            self.image_list.extend(list(folder.glob(f'*{ext.upper()}')))
        
        self.image_list.sort()
        
        if self.image_list:
            self.current_index = 0
            self.load_current_image()
            self.logger.info(f"加载了 {len(self.image_list)} 张图像")
        else:
            messagebox.showwarning("警告", "文件夹中没有找到图像文件")
    
    def load_current_image(self) -> None:
        """加载当前图像"""
        if not self.image_list or self.current_index >= len(self.image_list):
            return
        
        self.current_image_path = self.image_list[self.current_index]
        
        # 读取图像
        self.current_image = cv2.imread(str(self.current_image_path))
        if self.current_image is None:
            messagebox.showerror("错误", f"无法读取图像: {self.current_image_path}")
            return
        
        # 显示图像
        self.display_image()
        
        # 更新信息标签
        self.image_info_label.config(text=f"{self.current_index + 1}/{len(self.image_list)}")
        
        # 尝试加载已有标注
        self.load_existing_annotation()
    
    def display_image(self) -> None:
        """显示图像"""
        if self.current_image is None:
            return
        
        # 转换颜色空间
        image_rgb = cv2.cvtColor(self.current_image, cv2.COLOR_BGR2RGB)
        
        # 调整图像大小以适应显示
        display_size = (800, 600)
        h, w = image_rgb.shape[:2]
        
        # 计算缩放比例
        scale = min(display_size[0] / w, display_size[1] / h)
        new_w, new_h = int(w * scale), int(h * scale)
        
        # 缩放图像
        resized_image = cv2.resize(image_rgb, (new_w, new_h))
        
        # 转换为PIL图像
        pil_image = Image.fromarray(resized_image)
        photo = ImageTk.PhotoImage(pil_image)
        
        # 更新标签
        self.image_label.config(image=photo, text="")
        self.image_label.image = photo  # 保持引用
    
    def prev_image(self) -> None:
        """上一张图像"""
        if self.image_list and self.current_index > 0:
            self.current_index -= 1
            self.load_current_image()
    
    def next_image(self) -> None:
        """下一张图像"""
        if self.image_list and self.current_index < len(self.image_list) - 1:
            self.current_index += 1
            self.load_current_image()
    
    def on_plate_number_change(self, *args) -> None:
        """车牌号码变化时的回调"""
        plate_number = self.plate_number_var.get()
        
        # 更新字符列表
        self.char_listbox.delete(0, tk.END)
        for i, char in enumerate(plate_number):
            self.char_listbox.insert(tk.END, f"{i+1}: {char}")
        
        # 更新当前标注
        self.current_annotation['plate_number'] = plate_number
        self.current_annotation['characters'] = [{'char': char, 'position': i} 
                                                for i, char in enumerate(plate_number)]
    
    def on_char_select(self, event) -> None:
        """字符选择时的回调"""
        selection = self.char_listbox.curselection()
        if selection:
            index = selection[0]
            plate_number = self.plate_number_var.get()
            if index < len(plate_number):
                self.char_var.set(plate_number[index])
    
    def update_char(self) -> None:
        """更新字符"""
        selection = self.char_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要更新的字符")
            return
        
        index = selection[0]
        new_char = self.char_var.get()
        
        if not new_char:
            messagebox.showwarning("警告", "字符不能为空")
            return
        
        # 更新车牌号码
        plate_number = list(self.plate_number_var.get())
        if index < len(plate_number):
            plate_number[index] = new_char
            self.plate_number_var.set(''.join(plate_number))
    
    def save_annotation(self) -> None:
        """保存标注"""
        if not self.current_image_path:
            messagebox.showwarning("警告", "没有当前图像")
            return
        
        # 准备标注数据
        annotation_data = {
            'image_filename': self.current_image_path.name,
            'image_path': str(self.current_image_path),
            'plate_number': self.plate_number_var.get(),
            'plate_color': self.plate_color_var.get(),
            'characters': self.current_annotation.get('characters', []),
            'annotation_tool': 'manual',
            'annotated_by': 'user'
        }
        
        # 保存到JSON文件
        annotation_path = self.current_image_path.with_suffix('.json')
        try:
            with open(annotation_path, 'w', encoding='utf-8') as f:
                json.dump(annotation_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"标注已保存: {annotation_path}")
            messagebox.showinfo("成功", "标注已保存")
            
        except Exception as e:
            self.logger.error(f"保存标注失败: {str(e)}")
            messagebox.showerror("错误", f"保存标注失败: {str(e)}")
    
    def load_annotation(self) -> None:
        """加载标注"""
        self.load_existing_annotation()
    
    def load_existing_annotation(self) -> None:
        """加载已有标注"""
        if not self.current_image_path:
            return
        
        annotation_path = self.current_image_path.with_suffix('.json')
        if annotation_path.exists():
            try:
                with open(annotation_path, 'r', encoding='utf-8') as f:
                    annotation_data = json.load(f)
                
                # 更新界面
                self.plate_number_var.set(annotation_data.get('plate_number', ''))
                self.plate_color_var.set(annotation_data.get('plate_color', 'blue'))
                
                self.current_annotation = annotation_data
                
                self.logger.info(f"加载标注: {annotation_path}")
                
            except Exception as e:
                self.logger.warning(f"加载标注失败 {annotation_path}: {str(e)}")
        else:
            # 清空界面
            self.plate_number_var.set('')
            self.plate_color_var.set('blue')
            self.current_annotation = {}


def create_annotation_tool(config: Dict[str, Any]) -> AnnotationTool:
    """
    创建标注工具实例
    
    Args:
        config (Dict[str, Any]): 配置字典
        
    Returns:
        AnnotationTool: 标注工具实例
    """
    return AnnotationTool(config)
