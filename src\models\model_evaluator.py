#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型评估器
Model Evaluator

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
from sklearn.metrics import precision_recall_fscore_support
import time
from tqdm import tqdm

from ..utils.logger import LoggerMixin


class ModelEvaluator(LoggerMixin):
    """模型评估器"""
    
    def __init__(self, model: nn.Module, device: Optional[torch.device] = None):
        """
        初始化模型评估器
        
        Args:
            model (nn.Module): 要评估的模型
            device (Optional[torch.device]): 计算设备
        """
        self.model = model
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)
        
        self.logger.info(f"模型评估器初始化完成，使用设备: {self.device}")
    
    def evaluate_classification(self, test_loader: DataLoader, 
                              class_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        评估分类模型
        
        Args:
            test_loader (DataLoader): 测试数据加载器
            class_names (Optional[List[str]]): 类别名称列表
            
        Returns:
            Dict[str, Any]: 评估结果
        """
        self.model.eval()
        
        all_predictions = []
        all_targets = []
        all_probabilities = []
        total_time = 0.0
        
        self.logger.info("开始评估分类模型...")
        
        with torch.no_grad():
            for data, targets in tqdm(test_loader, desc='Evaluating'):
                data, targets = data.to(self.device), targets.to(self.device)
                
                # 计算推理时间
                start_time = time.time()
                outputs = self.model(data)
                inference_time = time.time() - start_time
                total_time += inference_time
                
                # 获取预测结果
                probabilities = torch.softmax(outputs, dim=1)
                _, predicted = torch.max(outputs, 1)
                
                # 收集结果
                all_predictions.extend(predicted.cpu().numpy())
                all_targets.extend(targets.cpu().numpy())
                all_probabilities.extend(probabilities.cpu().numpy())
        
        # 计算基本指标
        accuracy = np.mean(np.array(all_predictions) == np.array(all_targets))
        avg_inference_time = total_time / len(test_loader.dataset)
        
        # 计算详细指标
        precision, recall, f1, support = precision_recall_fscore_support(
            all_targets, all_predictions, average=None, zero_division=0
        )
        
        # 计算宏平均和微平均
        macro_precision, macro_recall, macro_f1, _ = precision_recall_fscore_support(
            all_targets, all_predictions, average='macro', zero_division=0
        )
        
        micro_precision, micro_recall, micro_f1, _ = precision_recall_fscore_support(
            all_targets, all_predictions, average='micro', zero_division=0
        )
        
        # 计算混淆矩阵
        cm = confusion_matrix(all_targets, all_predictions)
        
        # 计算每类准确率
        class_accuracies = cm.diagonal() / cm.sum(axis=1)
        class_accuracies = np.nan_to_num(class_accuracies)  # 处理除零情况
        
        # 构建结果字典
        results = {
            'overall_accuracy': accuracy,
            'macro_precision': macro_precision,
            'macro_recall': macro_recall,
            'macro_f1': macro_f1,
            'micro_precision': micro_precision,
            'micro_recall': micro_recall,
            'micro_f1': micro_f1,
            'class_precision': precision.tolist(),
            'class_recall': recall.tolist(),
            'class_f1': f1.tolist(),
            'class_accuracies': class_accuracies.tolist(),
            'class_support': support.tolist(),
            'confusion_matrix': cm.tolist(),
            'avg_inference_time': avg_inference_time,
            'total_samples': len(all_targets),
            'predictions': all_predictions,
            'targets': all_targets,
            'probabilities': all_probabilities
        }
        
        if class_names:
            results['class_names'] = class_names
        
        self.logger.info(f"分类评估完成 - 准确率: {accuracy:.4f}, 宏F1: {macro_f1:.4f}")
        
        return results
    
    def evaluate_sequence_model(self, test_loader: DataLoader,
                              char_mapping: Dict[str, int]) -> Dict[str, Any]:
        """
        评估序列模型（如CRNN）
        
        Args:
            test_loader (DataLoader): 测试数据加载器
            char_mapping (Dict[str, int]): 字符映射
            
        Returns:
            Dict[str, Any]: 评估结果
        """
        self.model.eval()
        
        total_sequences = 0
        correct_sequences = 0
        total_characters = 0
        correct_characters = 0
        edit_distances = []
        
        # 创建反向映射
        reverse_mapping = {v: k for k, v in char_mapping.items()}
        
        self.logger.info("开始评估序列模型...")
        
        with torch.no_grad():
            for data, targets, lengths in tqdm(test_loader, desc='Evaluating Sequences'):
                data = data.to(self.device)
                
                # 前向传播
                outputs = self.model(data)
                
                # 解码预测结果
                batch_size = data.size(0)
                for i in range(batch_size):
                    # 获取预测序列
                    pred_sequence = self._decode_sequence_output(
                        outputs[i], reverse_mapping
                    )
                    
                    # 获取真实序列
                    target_length = lengths[i].item()
                    target_sequence = self._decode_target_sequence(
                        targets[i][:target_length], reverse_mapping
                    )
                    
                    # 计算序列准确率
                    total_sequences += 1
                    if pred_sequence == target_sequence:
                        correct_sequences += 1
                    
                    # 计算字符准确率
                    char_correct = sum(1 for p, t in zip(pred_sequence, target_sequence) if p == t)
                    total_characters += len(target_sequence)
                    correct_characters += char_correct
                    
                    # 计算编辑距离
                    edit_dist = self._calculate_edit_distance(pred_sequence, target_sequence)
                    edit_distances.append(edit_dist)
        
        # 计算指标
        sequence_accuracy = correct_sequences / total_sequences if total_sequences > 0 else 0.0
        character_accuracy = correct_characters / total_characters if total_characters > 0 else 0.0
        avg_edit_distance = np.mean(edit_distances) if edit_distances else 0.0
        
        results = {
            'sequence_accuracy': sequence_accuracy,
            'character_accuracy': character_accuracy,
            'average_edit_distance': avg_edit_distance,
            'total_sequences': total_sequences,
            'correct_sequences': correct_sequences,
            'total_characters': total_characters,
            'correct_characters': correct_characters,
            'edit_distances': edit_distances
        }
        
        self.logger.info(f"序列评估完成 - 序列准确率: {sequence_accuracy:.4f}, 字符准确率: {character_accuracy:.4f}")
        
        return results
    
    def _decode_sequence_output(self, output: torch.Tensor, 
                              reverse_mapping: Dict[int, str]) -> str:
        """
        解码序列输出
        
        Args:
            output (torch.Tensor): 模型输出
            reverse_mapping (Dict[int, str]): 反向字符映射
            
        Returns:
            str: 解码后的字符串
        """
        # 简单的贪婪解码
        predicted_indices = torch.argmax(output, dim=-1)
        
        # 去除重复和空白字符
        decoded_chars = []
        prev_char = None
        
        for idx in predicted_indices:
            char_idx = idx.item()
            if char_idx != 0 and char_idx != prev_char:  # 0是空白字符
                if char_idx in reverse_mapping:
                    decoded_chars.append(reverse_mapping[char_idx])
            prev_char = char_idx
        
        return ''.join(decoded_chars)
    
    def _decode_target_sequence(self, target: torch.Tensor,
                              reverse_mapping: Dict[int, str]) -> str:
        """
        解码目标序列
        
        Args:
            target (torch.Tensor): 目标序列
            reverse_mapping (Dict[int, str]): 反向字符映射
            
        Returns:
            str: 解码后的字符串
        """
        decoded_chars = []
        for idx in target:
            char_idx = idx.item()
            if char_idx in reverse_mapping:
                decoded_chars.append(reverse_mapping[char_idx])
        
        return ''.join(decoded_chars)
    
    def _calculate_edit_distance(self, pred: str, target: str) -> int:
        """
        计算编辑距离（Levenshtein距离）
        
        Args:
            pred (str): 预测字符串
            target (str): 目标字符串
            
        Returns:
            int: 编辑距离
        """
        m, n = len(pred), len(target)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        # 初始化
        for i in range(m + 1):
            dp[i][0] = i
        for j in range(n + 1):
            dp[0][j] = j
        
        # 动态规划
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if pred[i-1] == target[j-1]:
                    dp[i][j] = dp[i-1][j-1]
                else:
                    dp[i][j] = 1 + min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1])
        
        return dp[m][n]
    
    def plot_confusion_matrix(self, confusion_matrix: np.ndarray,
                            class_names: Optional[List[str]] = None,
                            save_path: Optional[str] = None) -> None:
        """
        绘制混淆矩阵
        
        Args:
            confusion_matrix (np.ndarray): 混淆矩阵
            class_names (Optional[List[str]]): 类别名称
            save_path (Optional[str]): 保存路径
        """
        plt.figure(figsize=(10, 8))
        
        # 归一化混淆矩阵
        cm_normalized = confusion_matrix.astype('float') / confusion_matrix.sum(axis=1)[:, np.newaxis]
        cm_normalized = np.nan_to_num(cm_normalized)
        
        # 绘制热力图
        sns.heatmap(cm_normalized, annot=True, fmt='.2f', cmap='Blues',
                   xticklabels=class_names, yticklabels=class_names)
        
        plt.title('归一化混淆矩阵')
        plt.xlabel('预测标签')
        plt.ylabel('真实标签')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"混淆矩阵已保存: {save_path}")
        
        plt.show()
    
    def plot_class_performance(self, results: Dict[str, Any],
                             save_path: Optional[str] = None) -> None:
        """
        绘制各类别性能图
        
        Args:
            results (Dict[str, Any]): 评估结果
            save_path (Optional[str]): 保存路径
        """
        class_names = results.get('class_names', [f'Class_{i}' for i in range(len(results['class_precision']))])
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 精确率
        axes[0, 0].bar(range(len(results['class_precision'])), results['class_precision'])
        axes[0, 0].set_title('各类别精确率')
        axes[0, 0].set_xlabel('类别')
        axes[0, 0].set_ylabel('精确率')
        axes[0, 0].set_xticks(range(len(class_names)))
        axes[0, 0].set_xticklabels(class_names, rotation=45)
        
        # 召回率
        axes[0, 1].bar(range(len(results['class_recall'])), results['class_recall'])
        axes[0, 1].set_title('各类别召回率')
        axes[0, 1].set_xlabel('类别')
        axes[0, 1].set_ylabel('召回率')
        axes[0, 1].set_xticks(range(len(class_names)))
        axes[0, 1].set_xticklabels(class_names, rotation=45)
        
        # F1分数
        axes[1, 0].bar(range(len(results['class_f1'])), results['class_f1'])
        axes[1, 0].set_title('各类别F1分数')
        axes[1, 0].set_xlabel('类别')
        axes[1, 0].set_ylabel('F1分数')
        axes[1, 0].set_xticks(range(len(class_names)))
        axes[1, 0].set_xticklabels(class_names, rotation=45)
        
        # 支持数量
        axes[1, 1].bar(range(len(results['class_support'])), results['class_support'])
        axes[1, 1].set_title('各类别样本数量')
        axes[1, 1].set_xlabel('类别')
        axes[1, 1].set_ylabel('样本数量')
        axes[1, 1].set_xticks(range(len(class_names)))
        axes[1, 1].set_xticklabels(class_names, rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"类别性能图已保存: {save_path}")
        
        plt.show()
    
    def generate_classification_report(self, results: Dict[str, Any]) -> str:
        """
        生成分类报告
        
        Args:
            results (Dict[str, Any]): 评估结果
            
        Returns:
            str: 分类报告
        """
        class_names = results.get('class_names', [f'Class_{i}' for i in range(len(results['class_precision']))])
        
        report = classification_report(
            results['targets'],
            results['predictions'],
            target_names=class_names,
            digits=4
        )
        
        return report
    
    def benchmark_inference_speed(self, test_loader: DataLoader, 
                                num_runs: int = 100) -> Dict[str, float]:
        """
        基准测试推理速度
        
        Args:
            test_loader (DataLoader): 测试数据加载器
            num_runs (int): 运行次数
            
        Returns:
            Dict[str, float]: 速度基准测试结果
        """
        self.model.eval()
        
        # 预热
        with torch.no_grad():
            for data, _ in test_loader:
                data = data.to(self.device)
                _ = self.model(data)
                break
        
        # 基准测试
        times = []
        batch_sizes = []
        
        with torch.no_grad():
            for i, (data, _) in enumerate(test_loader):
                if i >= num_runs:
                    break
                
                data = data.to(self.device)
                batch_size = data.size(0)
                
                # 同步GPU
                if self.device.type == 'cuda':
                    torch.cuda.synchronize()
                
                start_time = time.time()
                _ = self.model(data)
                
                if self.device.type == 'cuda':
                    torch.cuda.synchronize()
                
                end_time = time.time()
                
                times.append(end_time - start_time)
                batch_sizes.append(batch_size)
        
        # 计算统计信息
        avg_batch_time = np.mean(times)
        avg_batch_size = np.mean(batch_sizes)
        avg_sample_time = avg_batch_time / avg_batch_size
        throughput = avg_batch_size / avg_batch_time
        
        results = {
            'avg_batch_time': avg_batch_time,
            'avg_sample_time': avg_sample_time,
            'throughput_samples_per_second': throughput,
            'std_batch_time': np.std(times),
            'min_batch_time': np.min(times),
            'max_batch_time': np.max(times),
            'num_runs': len(times)
        }
        
        self.logger.info(f"推理速度基准测试完成 - 平均每样本时间: {avg_sample_time*1000:.2f}ms, 吞吐量: {throughput:.1f} samples/s")
        
        return results
    
    def analyze_prediction_confidence(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析预测置信度
        
        Args:
            results (Dict[str, Any]): 评估结果
            
        Returns:
            Dict[str, Any]: 置信度分析结果
        """
        probabilities = np.array(results['probabilities'])
        predictions = np.array(results['predictions'])
        targets = np.array(results['targets'])
        
        # 计算最大置信度
        max_confidences = np.max(probabilities, axis=1)
        
        # 分析正确和错误预测的置信度
        correct_mask = predictions == targets
        correct_confidences = max_confidences[correct_mask]
        incorrect_confidences = max_confidences[~correct_mask]
        
        analysis = {
            'avg_confidence': np.mean(max_confidences),
            'avg_correct_confidence': np.mean(correct_confidences) if len(correct_confidences) > 0 else 0.0,
            'avg_incorrect_confidence': np.mean(incorrect_confidences) if len(incorrect_confidences) > 0 else 0.0,
            'confidence_std': np.std(max_confidences),
            'min_confidence': np.min(max_confidences),
            'max_confidence': np.max(max_confidences),
            'confidence_distribution': {
                'high_confidence_correct': np.sum((correct_mask) & (max_confidences > 0.9)),
                'high_confidence_incorrect': np.sum((~correct_mask) & (max_confidences > 0.9)),
                'low_confidence_correct': np.sum((correct_mask) & (max_confidences < 0.5)),
                'low_confidence_incorrect': np.sum((~correct_mask) & (max_confidences < 0.5))
            }
        }
        
        self.logger.info(f"置信度分析完成 - 平均置信度: {analysis['avg_confidence']:.4f}")
        
        return analysis
