#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练主模块
Training Main Module

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import argparse
import json
from pathlib import Path
from typing import Dict, Any, List

from ..utils.config_loader import load_config
from ..utils.logger import LoggerMixin
from .training_manager import TrainingManager


class TrainingMain(LoggerMixin):
    """训练主程序"""
    
    def __init__(self):
        """初始化训练主程序"""
        self.manager = None
    
    def run_single_training(self, args) -> None:
        """运行单模型训练"""
        # 加载配置
        config = load_config(args.config)
        
        # 创建训练管理器
        self.manager = TrainingManager(config)
        
        # 创建实验
        experiment_dir = self.manager.create_experiment(args.experiment_name)
        
        # 准备数据集
        train_dataset, val_dataset, test_dataset = self.manager.prepare_datasets(
            args.data_dir, args.train_ratio, args.val_ratio
        )
        
        # 解析模型参数
        model_params = {}
        if args.model_params:
            model_params = json.loads(args.model_params)
        
        # 解析训练参数
        training_params = {}
        if args.training_params:
            training_params = json.loads(args.training_params)
        
        # 训练模型
        results = self.manager.train_single_model(
            args.model_type,
            train_dataset,
            val_dataset,
            model_params,
            training_params
        )
        
        # 生成报告
        report = self.manager.generate_experiment_report()
        
        self.logger.info(f"单模型训练完成，最佳验证准确率: {results['best_val_accuracy']:.4f}")
        print(f"实验目录: {experiment_dir}")
        print(f"最佳验证准确率: {results['best_val_accuracy']:.4f}")
    
    def run_multi_training(self, args) -> None:
        """运行多模型训练"""
        # 加载配置
        config = load_config(args.config)
        
        # 创建训练管理器
        self.manager = TrainingManager(config)
        
        # 创建实验
        experiment_dir = self.manager.create_experiment(args.experiment_name)
        
        # 准备数据集
        train_dataset, val_dataset, test_dataset = self.manager.prepare_datasets(
            args.data_dir, args.train_ratio, args.val_ratio
        )
        
        # 解析模型类型列表
        model_types = args.model_types.split(',')
        
        # 解析参数列表
        model_params_list = []
        training_params_list = []
        
        if args.model_params_list:
            model_params_list = json.loads(args.model_params_list)
        
        if args.training_params_list:
            training_params_list = json.loads(args.training_params_list)
        
        # 训练多个模型
        all_results = self.manager.train_multiple_models(
            model_types,
            train_dataset,
            val_dataset,
            model_params_list,
            training_params_list
        )
        
        # 生成报告
        report = self.manager.generate_experiment_report()
        
        # 打印结果摘要
        self.logger.info("多模型训练完成")
        print(f"实验目录: {experiment_dir}")
        print("\n模型性能摘要:")
        for model_type, results in all_results.items():
            if 'error' not in results:
                print(f"  {model_type}: {results['best_val_accuracy']:.4f}")
            else:
                print(f"  {model_type}: 训练失败 - {results['error']}")
    
    def run_hyperparameter_optimization(self, args) -> None:
        """运行超参数优化"""
        # 加载配置
        config = load_config(args.config)
        
        # 创建训练管理器
        self.manager = TrainingManager(config)
        
        # 创建实验
        experiment_dir = self.manager.create_experiment(args.experiment_name)
        
        # 准备数据集
        train_dataset, val_dataset, test_dataset = self.manager.prepare_datasets(
            args.data_dir, args.train_ratio, args.val_ratio
        )
        
        # 运行超参数优化
        results = self.manager.optimize_hyperparameters(
            args.model_type,
            train_dataset,
            val_dataset,
            args.optimization_method,
            args.n_trials
        )
        
        # 使用最佳参数训练最终模型
        if args.train_best_model:
            self.logger.info("使用最佳参数训练最终模型...")
            
            best_results = self.manager.train_single_model(
                args.model_type,
                train_dataset,
                val_dataset,
                {},  # 模型参数在超参数优化中已确定
                results['best_params']
            )
            
            self.logger.info(f"最终模型训练完成，验证准确率: {best_results['best_val_accuracy']:.4f}")
        
        # 生成报告
        report = self.manager.generate_experiment_report()
        
        self.logger.info(f"超参数优化完成，最佳分数: {results['best_score']:.4f}")
        print(f"实验目录: {experiment_dir}")
        print(f"最佳分数: {results['best_score']:.4f}")
        print(f"最佳参数: {results['best_params']}")
    
    def run_full_pipeline(self, args) -> None:
        """运行完整训练流水线"""
        # 加载配置
        config = load_config(args.config)
        
        # 创建训练管理器
        self.manager = TrainingManager(config)
        
        # 创建实验
        experiment_dir = self.manager.create_experiment(args.experiment_name)
        
        # 准备数据集
        train_dataset, val_dataset, test_dataset = self.manager.prepare_datasets(
            args.data_dir, args.train_ratio, args.val_ratio
        )
        
        # 1. 基础模型训练
        self.logger.info("步骤1: 基础模型训练")
        model_types = ['cnn', 'crnn', 'transformer']
        
        baseline_results = self.manager.train_multiple_models(
            model_types,
            train_dataset,
            val_dataset
        )
        
        # 找到最佳基础模型
        best_baseline_model = max(
            [(k, v) for k, v in baseline_results.items() if 'error' not in v],
            key=lambda x: x[1]['best_val_accuracy']
        )
        
        self.logger.info(f"最佳基础模型: {best_baseline_model[0]} ({best_baseline_model[1]['best_val_accuracy']:.4f})")
        
        # 2. 超参数优化
        if args.optimize_hyperparameters:
            self.logger.info("步骤2: 超参数优化")
            
            optimization_results = self.manager.optimize_hyperparameters(
                best_baseline_model[0],
                train_dataset,
                val_dataset,
                'bayesian',
                args.n_trials
            )
            
            # 3. 最终模型训练
            self.logger.info("步骤3: 最终模型训练")
            
            final_results = self.manager.train_single_model(
                best_baseline_model[0],
                train_dataset,
                val_dataset,
                {},
                optimization_results['best_params']
            )
            
            self.logger.info(f"最终模型性能: {final_results['best_val_accuracy']:.4f}")
        
        # 生成报告
        report = self.manager.generate_experiment_report()
        
        self.logger.info("完整训练流水线完成")
        print(f"实验目录: {experiment_dir}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='车牌字符识别模型训练')
    
    # 通用参数
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--data-dir', type=str, required=True,
                       help='数据目录路径')
    parser.add_argument('--experiment-name', type=str, required=True,
                       help='实验名称')
    parser.add_argument('--train-ratio', type=float, default=0.7,
                       help='训练集比例')
    parser.add_argument('--val-ratio', type=float, default=0.2,
                       help='验证集比例')
    
    # 子命令
    subparsers = parser.add_subparsers(dest='mode', help='运行模式')
    
    # 单模型训练
    single_parser = subparsers.add_parser('single', help='单模型训练')
    single_parser.add_argument('--model-type', type=str, 
                              choices=['cnn', 'crnn', 'transformer'],
                              required=True, help='模型类型')
    single_parser.add_argument('--model-params', type=str,
                              help='模型参数 (JSON格式)')
    single_parser.add_argument('--training-params', type=str,
                              help='训练参数 (JSON格式)')
    
    # 多模型训练
    multi_parser = subparsers.add_parser('multi', help='多模型训练')
    multi_parser.add_argument('--model-types', type=str, required=True,
                             help='模型类型列表 (逗号分隔)')
    multi_parser.add_argument('--model-params-list', type=str,
                             help='模型参数列表 (JSON格式)')
    multi_parser.add_argument('--training-params-list', type=str,
                             help='训练参数列表 (JSON格式)')
    
    # 超参数优化
    optim_parser = subparsers.add_parser('optimize', help='超参数优化')
    optim_parser.add_argument('--model-type', type=str,
                             choices=['cnn', 'crnn', 'transformer'],
                             required=True, help='模型类型')
    optim_parser.add_argument('--optimization-method', type=str,
                             choices=['bayesian', 'random', 'grid'],
                             default='bayesian', help='优化方法')
    optim_parser.add_argument('--n-trials', type=int, default=50,
                             help='优化试验次数')
    optim_parser.add_argument('--train-best-model', action='store_true',
                             help='使用最佳参数训练最终模型')
    
    # 完整流水线
    pipeline_parser = subparsers.add_parser('pipeline', help='完整训练流水线')
    pipeline_parser.add_argument('--optimize-hyperparameters', action='store_true',
                                help='是否进行超参数优化')
    pipeline_parser.add_argument('--n-trials', type=int, default=30,
                                help='超参数优化试验次数')
    
    args = parser.parse_args()
    
    if not args.mode:
        parser.print_help()
        return
    
    # 创建训练主程序
    training_main = TrainingMain()
    
    try:
        if args.mode == 'single':
            training_main.run_single_training(args)
        elif args.mode == 'multi':
            training_main.run_multi_training(args)
        elif args.mode == 'optimize':
            training_main.run_hyperparameter_optimization(args)
        elif args.mode == 'pipeline':
            training_main.run_full_pipeline(args)
        else:
            print(f"不支持的模式: {args.mode}")
    
    except KeyboardInterrupt:
        print("\n训练被用户中断")
    except Exception as e:
        print(f"训练过程中出现错误: {str(e)}")
        raise


if __name__ == '__main__':
    main()
