#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集生成器
Dataset Generator

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import cv2
import numpy as np
import json
import random
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
from sklearn.model_selection import train_test_split
import shutil
from tqdm import tqdm

from ..utils.logger import LoggerMixin


class DatasetGenerator(LoggerMixin):
    """数据集生成器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化生成器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.data_config = config.get('data', {})
        
        self.processed_data_path = Path(self.data_config.get('processed_data_path', 'data/processed'))
        self.dataset_path = Path(self.data_config.get('dataset_path', 'data/datasets'))
        
        # 数据集分割比例
        self.train_ratio = self.data_config.get('train_ratio', 0.8)
        self.val_ratio = self.data_config.get('val_ratio', 0.1)
        self.test_ratio = self.data_config.get('test_ratio', 0.1)
        
        # 字符类别映射
        self.char_to_idx = self._build_character_mapping()
        self.idx_to_char = {v: k for k, v in self.char_to_idx.items()}
        
        self.logger.info("数据集生成器初始化完成")
        self.logger.info(f"字符类别数: {len(self.char_to_idx)}")
    
    def _build_character_mapping(self) -> Dict[str, int]:
        """
        构建字符到索引的映射
        
        Returns:
            Dict[str, int]: 字符到索引的映射
        """
        # 中国省份简称
        provinces = ['京', '津', '沪', '渝', '冀', '豫', '云', '辽', '黑', '湘', '皖', '鲁', 
                    '新', '苏', '浙', '赣', '鄂', '桂', '甘', '晋', '蒙', '陕', '吉', '闽', 
                    '贵', '粤', '青', '藏', '川', '宁', '琼']
        
        # 字母（去除I和O避免混淆）
        letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 
                  'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
        
        # 数字
        digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
        
        # 合并所有字符
        all_chars = provinces + letters + digits
        
        # 创建映射，索引0保留给背景或未知字符
        char_to_idx = {'<UNKNOWN>': 0}
        for i, char in enumerate(all_chars, 1):
            char_to_idx[char] = i
        
        return char_to_idx
    
    def generate_datasets(self) -> Dict[str, Any]:
        """
        生成训练、验证和测试数据集
        
        Returns:
            Dict[str, Any]: 生成结果统计
        """
        self.logger.info("开始生成数据集")
        
        # 创建数据集目录
        self.dataset_path.mkdir(parents=True, exist_ok=True)
        
        # 收集所有数据
        all_data = self._collect_all_data()
        
        if not all_data:
            self.logger.error("没有找到可用的数据")
            return {'error': '没有找到可用的数据'}
        
        self.logger.info(f"收集到 {len(all_data)} 个数据样本")
        
        # 分割数据集
        train_data, val_data, test_data = self._split_dataset(all_data)
        
        # 生成各个数据集
        results = {}
        results['train'] = self._generate_split_dataset(train_data, 'train')
        results['val'] = self._generate_split_dataset(val_data, 'val')
        results['test'] = self._generate_split_dataset(test_data, 'test')
        
        # 保存字符映射
        self._save_character_mapping()
        
        # 生成数据集统计信息
        self._generate_dataset_statistics(results)
        
        self.logger.info("数据集生成完成")
        return results
    
    def _collect_all_data(self) -> List[Dict[str, Any]]:
        """
        收集所有处理后的数据

        Returns:
            List[Dict[str, Any]]: 数据列表
        """
        all_data = []

        # 首先检查原始数据中的标注信息
        raw_data_path = Path(self.config['data']['raw_data_path'])

        # 由于处理后的图像文件名是哈希值，无法直接与标注文件对应
        # 我们直接使用原始数据中的图像和标注文件
        self.logger.info("由于文件名映射问题，直接使用原始数据")

        for source_dir in raw_data_path.iterdir():
            if source_dir.is_dir() and source_dir.name != '.gitkeep':
                self.logger.info(f"收集原始数据源: {source_dir.name}")

                # 查找图像和标注目录
                images_dir = source_dir / 'images'
                annotations_dir = source_dir / 'annotations'

                if images_dir.exists() and annotations_dir.exists():
                    # 获取所有标注文件
                    annotation_files = list(annotations_dir.glob('*.json'))

                    for ann_file in annotation_files:
                        try:
                            with open(ann_file, 'r', encoding='utf-8') as f:
                                annotation = json.load(f)

                            # 根据标注文件查找对应的图像文件
                            img_filename = annotation.get('image_filename', ann_file.stem + '.jpg')
                            img_file = images_dir / img_filename

                            if img_file.exists():
                                data_item = {
                                    'image_path': str(img_file),
                                    'annotation_path': str(ann_file),
                                    'annotation': annotation,
                                    'source': source_dir.name,
                                    'has_annotation': True
                                }
                                all_data.append(data_item)
                            else:
                                self.logger.warning(f"找不到对应的图像文件: {img_file}")

                        except Exception as e:
                            self.logger.warning(f"读取标注文件失败 {ann_file}: {str(e)}")

                # 如果没有标注目录，尝试直接使用图像文件
                elif images_dir.exists():
                    self.logger.info(f"数据源 {source_dir.name} 没有标注目录，尝试从文件名推断")
                    image_files = list(images_dir.glob('*.jpg'))

                    for img_file in image_files:
                        plate_number = self._extract_plate_from_filename(img_file.name)
                        if plate_number:
                            data_item = {
                                'image_path': str(img_file),
                                'annotation_path': None,
                                'annotation': {'plate_number': plate_number},
                                'source': source_dir.name,
                                'has_annotation': False
                            }
                            all_data.append(data_item)
        
        return all_data
    
    def _extract_plate_from_filename(self, filename: str) -> Optional[str]:
        """
        从文件名中提取车牌号码
        
        Args:
            filename (str): 文件名
            
        Returns:
            Optional[str]: 车牌号码，如果无法提取则返回None
        """
        # 这里可以实现从文件名提取车牌号的逻辑
        # 例如，如果文件名包含车牌号信息
        # 目前返回None，表示无法从文件名提取
        return None
    
    def _split_dataset(self, all_data: List[Dict[str, Any]]) -> Tuple[List, List, List]:
        """
        分割数据集
        
        Args:
            all_data (List[Dict[str, Any]]): 所有数据
            
        Returns:
            Tuple[List, List, List]: 训练集、验证集、测试集
        """
        # 随机打乱数据
        random.shuffle(all_data)
        
        # 计算分割点
        total_size = len(all_data)
        train_size = int(total_size * self.train_ratio)
        val_size = int(total_size * self.val_ratio)
        
        # 分割数据
        train_data = all_data[:train_size]
        val_data = all_data[train_size:train_size + val_size]
        test_data = all_data[train_size + val_size:]
        
        self.logger.info(f"数据集分割: 训练集 {len(train_data)}, 验证集 {len(val_data)}, 测试集 {len(test_data)}")
        
        return train_data, val_data, test_data
    
    def _generate_split_dataset(self, data_list: List[Dict[str, Any]], split_name: str) -> Dict[str, Any]:
        """
        生成单个数据集分割
        
        Args:
            data_list (List[Dict[str, Any]]): 数据列表
            split_name (str): 分割名称 ('train', 'val', 'test')
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        split_dir = self.dataset_path / split_name
        split_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        images_dir = split_dir / 'images'
        labels_dir = split_dir / 'labels'
        images_dir.mkdir(exist_ok=True)
        labels_dir.mkdir(exist_ok=True)
        
        success_count = 0
        error_count = 0
        
        self.logger.info(f"生成 {split_name} 数据集，共 {len(data_list)} 个样本")
        
        for i, data_item in enumerate(tqdm(data_list, desc=f"生成{split_name}集")):
            try:
                # 复制图像文件
                src_image_path = Path(data_item['image_path'])
                dst_image_path = images_dir / f"{split_name}_{i:06d}.jpg"
                shutil.copy2(src_image_path, dst_image_path)
                
                # 生成标签文件
                label_data = self._generate_label_data(data_item, dst_image_path.name)
                
                # 保存标签
                label_path = labels_dir / f"{split_name}_{i:06d}.json"
                with open(label_path, 'w', encoding='utf-8') as f:
                    json.dump(label_data, f, ensure_ascii=False, indent=2)
                
                success_count += 1
                
            except Exception as e:
                self.logger.error(f"生成样本失败 {data_item['image_path']}: {str(e)}")
                error_count += 1
        
        # 生成数据集索引文件
        self._generate_dataset_index(split_dir, split_name, success_count)
        
        result = {
            'split_name': split_name,
            'total_samples': len(data_list),
            'success_count': success_count,
            'error_count': error_count,
            'dataset_path': str(split_dir)
        }
        
        self.logger.info(f"{split_name} 数据集生成完成: {success_count}/{len(data_list)} 成功")
        return result
    
    def _generate_label_data(self, data_item: Dict[str, Any], image_filename: str) -> Dict[str, Any]:
        """
        生成标签数据
        
        Args:
            data_item (Dict[str, Any]): 数据项
            image_filename (str): 图像文件名
            
        Returns:
            Dict[str, Any]: 标签数据
        """
        annotation = data_item['annotation']
        plate_number = annotation.get('plate_number', '')
        
        # 转换字符为索引
        char_indices = []
        for char in plate_number:
            idx = self.char_to_idx.get(char, 0)  # 0为未知字符
            char_indices.append(idx)
        
        label_data = {
            'image_filename': image_filename,
            'plate_number': plate_number,
            'char_indices': char_indices,
            'num_chars': len(plate_number),
            'source': data_item['source'],
            'has_annotation': data_item['has_annotation']
        }
        
        # 如果有详细的字符位置信息，也包含进来
        if 'characters' in annotation:
            label_data['characters'] = annotation['characters']
        
        if 'plate_color' in annotation:
            label_data['plate_color'] = annotation['plate_color']
        
        return label_data
    
    def _generate_dataset_index(self, split_dir: Path, split_name: str, sample_count: int) -> None:
        """
        生成数据集索引文件
        
        Args:
            split_dir (Path): 数据集目录
            split_name (str): 分割名称
            sample_count (int): 样本数量
        """
        index_data = {
            'split_name': split_name,
            'sample_count': sample_count,
            'images_dir': 'images',
            'labels_dir': 'labels',
            'image_format': 'jpg',
            'label_format': 'json',
            'character_classes': len(self.char_to_idx),
            'description': f'{split_name} dataset for license plate recognition'
        }
        
        index_path = split_dir / 'dataset_index.json'
        with open(index_path, 'w', encoding='utf-8') as f:
            json.dump(index_data, f, ensure_ascii=False, indent=2)
    
    def _save_character_mapping(self) -> None:
        """保存字符映射"""
        mapping_data = {
            'char_to_idx': self.char_to_idx,
            'idx_to_char': self.idx_to_char,
            'num_classes': len(self.char_to_idx),
            'description': 'Character mapping for license plate recognition'
        }
        
        mapping_path = self.dataset_path / 'character_mapping.json'
        with open(mapping_path, 'w', encoding='utf-8') as f:
            json.dump(mapping_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"字符映射已保存: {mapping_path}")
    
    def _generate_dataset_statistics(self, results: Dict[str, Any]) -> None:
        """
        生成数据集统计信息
        
        Args:
            results (Dict[str, Any]): 生成结果
        """
        stats = {
            'dataset_info': {
                'total_samples': sum(r['success_count'] for r in results.values()),
                'train_samples': results['train']['success_count'],
                'val_samples': results['val']['success_count'],
                'test_samples': results['test']['success_count'],
                'character_classes': len(self.char_to_idx),
                'train_ratio': self.train_ratio,
                'val_ratio': self.val_ratio,
                'test_ratio': self.test_ratio
            },
            'generation_results': results,
            'character_mapping_info': {
                'total_characters': len(self.char_to_idx),
                'provinces': 31,
                'letters': 24,
                'digits': 10
            }
        }
        
        stats_path = self.dataset_path / 'dataset_statistics.json'
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"数据集统计信息已保存: {stats_path}")
    
    def create_custom_dataset(self, data_sources: List[str], output_name: str) -> Dict[str, Any]:
        """
        创建自定义数据集
        
        Args:
            data_sources (List[str]): 数据源列表
            output_name (str): 输出数据集名称
            
        Returns:
            Dict[str, Any]: 创建结果
        """
        self.logger.info(f"创建自定义数据集: {output_name}")
        
        # 收集指定数据源的数据
        custom_data = []
        for source_name in data_sources:
            source_dir = self.processed_data_path / source_name
            if source_dir.exists():
                # 收集该数据源的数据
                image_files = list(source_dir.glob('*.jpg'))
                for img_file in image_files:
                    annotation_file = img_file.with_suffix('.json')
                    if annotation_file.exists():
                        try:
                            with open(annotation_file, 'r', encoding='utf-8') as f:
                                annotation = json.load(f)
                            
                            data_item = {
                                'image_path': str(img_file),
                                'annotation_path': str(annotation_file),
                                'annotation': annotation,
                                'source': source_name,
                                'has_annotation': True
                            }
                            custom_data.append(data_item)
                        except Exception as e:
                            self.logger.warning(f"读取标注失败 {annotation_file}: {str(e)}")
        
        if not custom_data:
            return {'error': f'没有找到指定数据源的数据: {data_sources}'}
        
        # 生成自定义数据集
        custom_dir = self.dataset_path / output_name
        custom_dir.mkdir(exist_ok=True)
        
        result = self._generate_split_dataset(custom_data, output_name)
        
        self.logger.info(f"自定义数据集创建完成: {output_name}")
        return result
