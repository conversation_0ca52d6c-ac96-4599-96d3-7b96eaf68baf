#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车牌识别系统
License Plate Recognition System

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import cv2
import torch
import numpy as np
from typing import Dict, Any, List, Tuple, Optional, Union
from pathlib import Path
import json
import time
from PIL import Image

from ..utils.logger import LoggerMixin
from ..utils.config_loader import load_config
from ..models.plate_detector import PlateDetector, PlateDetectorYOLO
from ..models.character_recognizer import CharacterRecognizer, CharacterRecognizerCRNN, CharacterRecognizerTransformer
from ..character_segmentation.character_segmenter import CharacterSegmenter, AdaptiveCharacterSegmenter
from ..data_preprocessing.image_processor import ImageProcessor


class LicensePlateRecognizer(LoggerMixin):
    """车牌识别系统主类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化车牌识别系统
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.system_config = config.get('system', {})
        
        # 设备配置
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 模型组件
        self.plate_detector = None
        self.character_segmenter = None
        self.character_recognizer = None
        
        # 预处理器
        self.image_processor = ImageProcessor(config)
        
        # 字符映射
        self.char_mapping = self._load_character_mapping()
        self.reverse_char_mapping = {v: k for k, v in self.char_mapping.items()}
        
        # 性能统计
        self.performance_stats = {
            'total_processed': 0,
            'successful_detections': 0,
            'successful_recognitions': 0,
            'avg_processing_time': 0.0,
            'avg_detection_time': 0.0,
            'avg_segmentation_time': 0.0,
            'avg_recognition_time': 0.0
        }
        
        self.logger.info(f"车牌识别系统初始化完成，使用设备: {self.device}")
    
    def _load_character_mapping(self) -> Dict[str, int]:
        """加载字符映射"""
        mapping_file = self.config.get('data', {}).get('char_mapping_file', 'data/char_mapping.json')
        mapping_path = Path(mapping_file)
        
        if mapping_path.exists():
            with open(mapping_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 创建默认映射
            return self._create_default_character_mapping()
    
    def _create_default_character_mapping(self) -> Dict[str, int]:
        """创建默认字符映射"""
        provinces = ['京', '津', '沪', '渝', '冀', '豫', '云', '辽', '黑', '湘', '皖', '鲁', 
                    '新', '苏', '浙', '赣', '鄂', '桂', '甘', '晋', '蒙', '陕', '吉', '闽', 
                    '贵', '粤', '青', '藏', '川', '宁', '琼']
        letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 
                  'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
        digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
        
        all_chars = provinces + letters + digits
        return {char: i + 1 for i, char in enumerate(all_chars)}
    
    def load_models(self, detector_path: str, recognizer_path: str,
                   detector_type: str = 'cnn', recognizer_type: str = 'cnn') -> None:
        """
        加载模型
        
        Args:
            detector_path (str): 检测器模型路径
            recognizer_path (str): 识别器模型路径
            detector_type (str): 检测器类型
            recognizer_type (str): 识别器类型
        """
        # 加载车牌检测器
        self._load_plate_detector(detector_path, detector_type)
        
        # 加载字符识别器
        self._load_character_recognizer(recognizer_path, recognizer_type)
        
        # 创建字符分割器
        segmenter_type = self.system_config.get('segmenter_type', 'adaptive')
        if segmenter_type == 'adaptive':
            self.character_segmenter = AdaptiveCharacterSegmenter(self.config)
        else:
            self.character_segmenter = CharacterSegmenter(self.config)
        
        self.logger.info("所有模型加载完成")
    
    def _load_plate_detector(self, model_path: str, model_type: str) -> None:
        """加载车牌检测器"""
        if model_type.lower() == 'yolo':
            self.plate_detector = PlateDetectorYOLO()
        else:
            self.plate_detector = PlateDetector()
        
        # 加载权重
        checkpoint = torch.load(model_path, map_location=self.device)
        if 'model_state_dict' in checkpoint:
            self.plate_detector.load_state_dict(checkpoint['model_state_dict'])
        else:
            self.plate_detector.load_state_dict(checkpoint)
        
        self.plate_detector.to(self.device)
        self.plate_detector.eval()
        
        self.logger.info(f"车牌检测器加载完成: {model_type}")
    
    def _load_character_recognizer(self, model_path: str, model_type: str) -> None:
        """加载字符识别器"""
        num_classes = len(self.char_mapping)
        
        if model_type.lower() == 'crnn':
            self.character_recognizer = CharacterRecognizerCRNN(num_classes=num_classes)
        elif model_type.lower() == 'transformer':
            self.character_recognizer = CharacterRecognizerTransformer(num_classes=num_classes)
        else:
            self.character_recognizer = CharacterRecognizer(num_classes=num_classes)
        
        # 加载权重
        checkpoint = torch.load(model_path, map_location=self.device)
        if 'model_state_dict' in checkpoint:
            self.character_recognizer.load_state_dict(checkpoint['model_state_dict'])
        else:
            self.character_recognizer.load_state_dict(checkpoint)
        
        self.character_recognizer.to(self.device)
        self.character_recognizer.eval()
        
        self.logger.info(f"字符识别器加载完成: {model_type}")
    
    def recognize_from_image(self, image: Union[str, np.ndarray],
                           return_details: bool = False) -> Union[str, Dict[str, Any]]:
        """
        从图像识别车牌
        
        Args:
            image (Union[str, np.ndarray]): 图像路径或图像数组
            return_details (bool): 是否返回详细信息
            
        Returns:
            Union[str, Dict[str, Any]]: 识别结果或详细信息
        """
        start_time = time.time()
        
        # 加载图像
        if isinstance(image, str):
            img = cv2.imread(image)
            if img is None:
                raise ValueError(f"无法加载图像: {image}")
        else:
            img = image.copy()
        
        # 预处理图像
        processed_img = self.image_processor.preprocess_image(img)
        
        # 检测车牌
        detection_start = time.time()
        plate_regions = self._detect_plates(processed_img)
        detection_time = time.time() - detection_start
        
        if not plate_regions:
            self.performance_stats['total_processed'] += 1
            if return_details:
                return {
                    'license_plate': '',
                    'confidence': 0.0,
                    'plate_regions': [],
                    'processing_time': time.time() - start_time,
                    'error': 'No license plate detected'
                }
            return ''
        
        self.performance_stats['successful_detections'] += 1
        
        # 处理每个检测到的车牌区域
        results = []
        for i, (plate_img, bbox, confidence) in enumerate(plate_regions):
            # 分割字符
            segmentation_start = time.time()
            char_images = self._segment_characters(plate_img)
            segmentation_time = time.time() - segmentation_start
            
            if not char_images:
                continue
            
            # 识别字符
            recognition_start = time.time()
            license_plate, char_confidences = self._recognize_characters(char_images)
            recognition_time = time.time() - recognition_start
            
            if license_plate:
                self.performance_stats['successful_recognitions'] += 1
                
                result = {
                    'license_plate': license_plate,
                    'confidence': np.mean(char_confidences),
                    'bbox': bbox,
                    'detection_confidence': confidence,
                    'character_confidences': char_confidences,
                    'processing_times': {
                        'detection': detection_time,
                        'segmentation': segmentation_time,
                        'recognition': recognition_time
                    }
                }
                results.append(result)
        
        # 更新性能统计
        total_time = time.time() - start_time
        self._update_performance_stats(total_time, detection_time, 
                                     segmentation_time if 'segmentation_time' in locals() else 0.0,
                                     recognition_time if 'recognition_time' in locals() else 0.0)
        
        if not results:
            if return_details:
                return {
                    'license_plate': '',
                    'confidence': 0.0,
                    'plate_regions': plate_regions,
                    'processing_time': total_time,
                    'error': 'Character recognition failed'
                }
            return ''
        
        # 返回置信度最高的结果
        best_result = max(results, key=lambda x: x['confidence'])
        
        if return_details:
            best_result['processing_time'] = total_time
            best_result['all_results'] = results
            return best_result
        
        return best_result['license_plate']
    
    def _detect_plates(self, image: np.ndarray) -> List[Tuple[np.ndarray, Tuple[int, int, int, int], float]]:
        """
        检测车牌区域
        
        Args:
            image (np.ndarray): 输入图像
            
        Returns:
            List[Tuple[np.ndarray, Tuple[int, int, int, int], float]]: 车牌图像、边界框、置信度
        """
        if self.plate_detector is None:
            raise ValueError("车牌检测器未加载")
        
        # 转换图像格式
        if len(image.shape) == 3:
            input_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            input_image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
        
        # 调整图像尺寸
        input_image = cv2.resize(input_image, (416, 416))  # YOLO输入尺寸
        input_tensor = torch.from_numpy(input_image).permute(2, 0, 1).float() / 255.0
        input_tensor = input_tensor.unsqueeze(0).to(self.device)
        
        # 检测
        with torch.no_grad():
            detections = self.plate_detector(input_tensor)
        
        # 解析检测结果
        plate_regions = []
        
        # 这里需要根据具体的检测器输出格式来解析
        # 简化实现，假设检测器返回边界框和置信度
        if hasattr(detections, 'shape') and len(detections.shape) > 1:
            # 假设检测结果格式为 [batch, num_detections, 6] (x1, y1, x2, y2, conf, class)
            for detection in detections[0]:
                if detection[4] > 0.5:  # 置信度阈值
                    x1, y1, x2, y2, conf = detection[:5]
                    
                    # 转换回原图坐标
                    h, w = image.shape[:2]
                    x1 = int(x1 * w / 416)
                    y1 = int(y1 * h / 416)
                    x2 = int(x2 * w / 416)
                    y2 = int(y2 * h / 416)
                    
                    # 提取车牌区域
                    plate_img = image[y1:y2, x1:x2]
                    if plate_img.size > 0:
                        plate_regions.append((plate_img, (x1, y1, x2, y2), float(conf)))
        
        return plate_regions
    
    def _segment_characters(self, plate_image: np.ndarray) -> List[np.ndarray]:
        """
        分割字符
        
        Args:
            plate_image (np.ndarray): 车牌图像
            
        Returns:
            List[np.ndarray]: 字符图像列表
        """
        if self.character_segmenter is None:
            raise ValueError("字符分割器未初始化")
        
        return self.character_segmenter.segment_characters(plate_image)
    
    def _recognize_characters(self, char_images: List[np.ndarray]) -> Tuple[str, List[float]]:
        """
        识别字符
        
        Args:
            char_images (List[np.ndarray]): 字符图像列表
            
        Returns:
            Tuple[str, List[float]]: 识别结果和置信度列表
        """
        if self.character_recognizer is None:
            raise ValueError("字符识别器未加载")
        
        license_plate = ""
        confidences = []
        
        for char_img in char_images:
            # 预处理字符图像
            processed_char = self._preprocess_character_image(char_img)
            
            # 识别
            with torch.no_grad():
                output = self.character_recognizer(processed_char.unsqueeze(0).to(self.device))
                probabilities = torch.softmax(output, dim=1)
                confidence, predicted = torch.max(probabilities, 1)
                
                # 转换为字符
                char_idx = predicted.item()
                if char_idx in self.reverse_char_mapping:
                    license_plate += self.reverse_char_mapping[char_idx]
                    confidences.append(confidence.item())
                else:
                    # 未知字符，跳过
                    pass
        
        return license_plate, confidences
    
    def _preprocess_character_image(self, char_image: np.ndarray) -> torch.Tensor:
        """
        预处理字符图像
        
        Args:
            char_image (np.ndarray): 字符图像
            
        Returns:
            torch.Tensor: 预处理后的张量
        """
        # 转换为PIL图像
        if len(char_image.shape) == 3:
            pil_image = Image.fromarray(cv2.cvtColor(char_image, cv2.COLOR_BGR2RGB))
        else:
            pil_image = Image.fromarray(char_image).convert('RGB')
        
        # 调整尺寸
        pil_image = pil_image.resize((64, 64), Image.LANCZOS)
        
        # 转换为张量并归一化
        import torchvision.transforms as transforms
        transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        return transform(pil_image)
    
    def _update_performance_stats(self, total_time: float, detection_time: float,
                                segmentation_time: float, recognition_time: float) -> None:
        """更新性能统计"""
        self.performance_stats['total_processed'] += 1
        
        # 更新平均时间
        n = self.performance_stats['total_processed']
        self.performance_stats['avg_processing_time'] = (
            (self.performance_stats['avg_processing_time'] * (n - 1) + total_time) / n
        )
        self.performance_stats['avg_detection_time'] = (
            (self.performance_stats['avg_detection_time'] * (n - 1) + detection_time) / n
        )
        self.performance_stats['avg_segmentation_time'] = (
            (self.performance_stats['avg_segmentation_time'] * (n - 1) + segmentation_time) / n
        )
        self.performance_stats['avg_recognition_time'] = (
            (self.performance_stats['avg_recognition_time'] * (n - 1) + recognition_time) / n
        )
    
    def batch_recognize(self, image_paths: List[str],
                       return_details: bool = False) -> List[Union[str, Dict[str, Any]]]:
        """
        批量识别车牌
        
        Args:
            image_paths (List[str]): 图像路径列表
            return_details (bool): 是否返回详细信息
            
        Returns:
            List[Union[str, Dict[str, Any]]]: 识别结果列表
        """
        results = []
        
        for i, image_path in enumerate(image_paths):
            self.logger.info(f"处理图像 {i+1}/{len(image_paths)}: {image_path}")
            
            try:
                result = self.recognize_from_image(image_path, return_details)
                results.append(result)
            except Exception as e:
                self.logger.error(f"处理图像 {image_path} 时出错: {str(e)}")
                if return_details:
                    results.append({
                        'license_plate': '',
                        'confidence': 0.0,
                        'error': str(e)
                    })
                else:
                    results.append('')
        
        return results
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            Dict[str, Any]: 性能统计
        """
        stats = self.performance_stats.copy()
        
        # 计算成功率
        if stats['total_processed'] > 0:
            stats['detection_success_rate'] = stats['successful_detections'] / stats['total_processed']
            stats['recognition_success_rate'] = stats['successful_recognitions'] / stats['total_processed']
        else:
            stats['detection_success_rate'] = 0.0
            stats['recognition_success_rate'] = 0.0
        
        return stats
    
    def reset_performance_stats(self) -> None:
        """重置性能统计"""
        self.performance_stats = {
            'total_processed': 0,
            'successful_detections': 0,
            'successful_recognitions': 0,
            'avg_processing_time': 0.0,
            'avg_detection_time': 0.0,
            'avg_segmentation_time': 0.0,
            'avg_recognition_time': 0.0
        }
    
    def save_performance_report(self, save_path: str) -> None:
        """
        保存性能报告
        
        Args:
            save_path (str): 保存路径
        """
        stats = self.get_performance_stats()
        
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"性能报告已保存: {save_path}")
    
    def visualize_detection(self, image: Union[str, np.ndarray],
                          save_path: Optional[str] = None) -> np.ndarray:
        """
        可视化检测结果
        
        Args:
            image (Union[str, np.ndarray]): 输入图像
            save_path (Optional[str]): 保存路径
            
        Returns:
            np.ndarray: 可视化结果图像
        """
        # 加载图像
        if isinstance(image, str):
            img = cv2.imread(image)
        else:
            img = image.copy()
        
        # 获取详细识别结果
        result = self.recognize_from_image(img, return_details=True)
        
        # 绘制检测框和识别结果
        vis_img = img.copy()
        
        if 'all_results' in result:
            for res in result['all_results']:
                bbox = res['bbox']
                license_plate = res['license_plate']
                confidence = res['confidence']
                
                # 绘制边界框
                cv2.rectangle(vis_img, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (0, 255, 0), 2)
                
                # 绘制识别结果
                text = f"{license_plate} ({confidence:.2f})"
                cv2.putText(vis_img, text, (bbox[0], bbox[1] - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        
        # 保存结果
        if save_path:
            cv2.imwrite(save_path, vis_img)
            self.logger.info(f"可视化结果已保存: {save_path}")
        
        return vis_img
