#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练管理器
Training Manager

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path
import json
import time
import shutil
from datetime import datetime

from ..utils.logger import LoggerMixin
from ..utils.config_loader import load_config
from ..models.model_trainer import ModelTrainer
from ..models.model_evaluator import ModelEvaluator
from ..models.character_dataset import CharacterDataset, create_character_transforms, create_data_loaders
from ..models.character_recognizer import CharacterRecognizer, CharacterRecognizerCRNN, CharacterRecognizerTransformer
from .hyperparameter_optimizer import HyperparameterOptimizer


class TrainingManager(LoggerMixin):
    """训练管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化训练管理器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.training_config = config.get('training', {})
        
        # 设备配置
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 字符映射
        self.char_mapping = self._load_character_mapping()
        self.num_classes = len(self.char_mapping)
        
        # 训练状态
        self.current_experiment = None
        self.experiment_dir = None
        
        self.logger.info(f"训练管理器初始化完成，字符类别数: {self.num_classes}")
    
    def _load_character_mapping(self) -> Dict[str, int]:
        """加载字符映射"""
        mapping_file = self.config.get('data', {}).get('char_mapping_file', 'data/char_mapping.json')
        mapping_path = Path(mapping_file)
        
        if mapping_path.exists():
            with open(mapping_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 创建默认映射
            return self._create_default_character_mapping()
    
    def _create_default_character_mapping(self) -> Dict[str, int]:
        """创建默认字符映射"""
        provinces = ['京', '津', '沪', '渝', '冀', '豫', '云', '辽', '黑', '湘', '皖', '鲁', 
                    '新', '苏', '浙', '赣', '鄂', '桂', '甘', '晋', '蒙', '陕', '吉', '闽', 
                    '贵', '粤', '青', '藏', '川', '宁', '琼']
        letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 
                  'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
        digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
        
        all_chars = provinces + letters + digits
        return {char: i + 1 for i, char in enumerate(all_chars)}
    
    def create_experiment(self, experiment_name: str, 
                         base_dir: str = 'experiments') -> str:
        """
        创建新的实验
        
        Args:
            experiment_name (str): 实验名称
            base_dir (str): 基础目录
            
        Returns:
            str: 实验目录路径
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        experiment_dir = Path(base_dir) / f"{experiment_name}_{timestamp}"
        experiment_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        (experiment_dir / 'models').mkdir(exist_ok=True)
        (experiment_dir / 'logs').mkdir(exist_ok=True)
        (experiment_dir / 'plots').mkdir(exist_ok=True)
        (experiment_dir / 'configs').mkdir(exist_ok=True)
        
        # 保存配置
        config_file = experiment_dir / 'configs' / 'config.json'
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
        
        self.current_experiment = experiment_name
        self.experiment_dir = str(experiment_dir)
        
        self.logger.info(f"创建实验: {experiment_name}，目录: {experiment_dir}")
        
        return str(experiment_dir)
    
    def prepare_datasets(self, data_dir: str, 
                        train_ratio: float = 0.7,
                        val_ratio: float = 0.2,
                        test_ratio: float = 0.1) -> Tuple[CharacterDataset, CharacterDataset, CharacterDataset]:
        """
        准备数据集
        
        Args:
            data_dir (str): 数据目录
            train_ratio (float): 训练集比例
            val_ratio (float): 验证集比例
            test_ratio (float): 测试集比例
            
        Returns:
            Tuple[CharacterDataset, CharacterDataset, CharacterDataset]: 训练、验证、测试数据集
        """
        # 验证比例
        if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
            raise ValueError("数据集比例之和必须等于1.0")
        
        # 创建数据变换
        train_transform = create_character_transforms(training=True)
        val_transform = create_character_transforms(training=False)
        
        # 加载完整数据集
        full_dataset = CharacterDataset(
            data_dir=data_dir,
            char_mapping=self.char_mapping,
            transform=None
        )
        
        # 分割数据集
        total_size = len(full_dataset)
        train_size = int(total_size * train_ratio)
        val_size = int(total_size * val_ratio)
        test_size = total_size - train_size - val_size
        
        # 随机分割
        indices = torch.randperm(total_size).tolist()
        train_indices = indices[:train_size]
        val_indices = indices[train_size:train_size + val_size]
        test_indices = indices[train_size + val_size:]
        
        # 创建子数据集
        train_samples = [full_dataset.samples[i] for i in train_indices]
        val_samples = [full_dataset.samples[i] for i in val_indices]
        test_samples = [full_dataset.samples[i] for i in test_indices]
        
        # 创建数据集对象
        train_dataset = CharacterDataset(data_dir, self.char_mapping, train_transform)
        train_dataset.samples = train_samples
        
        val_dataset = CharacterDataset(data_dir, self.char_mapping, val_transform)
        val_dataset.samples = val_samples
        
        test_dataset = CharacterDataset(data_dir, self.char_mapping, val_transform)
        test_dataset.samples = test_samples
        
        # 保存数据集分割信息
        if self.experiment_dir:
            split_info = {
                'total_samples': total_size,
                'train_samples': len(train_dataset),
                'val_samples': len(val_dataset),
                'test_samples': len(test_dataset),
                'train_indices': train_indices,
                'val_indices': val_indices,
                'test_indices': test_indices
            }
            
            split_file = Path(self.experiment_dir) / 'configs' / 'dataset_split.json'
            with open(split_file, 'w', encoding='utf-8') as f:
                json.dump(split_info, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"数据集准备完成 - 训练: {len(train_dataset)}, 验证: {len(val_dataset)}, 测试: {len(test_dataset)}")
        
        return train_dataset, val_dataset, test_dataset
    
    def train_single_model(self, model_type: str, 
                          train_dataset: CharacterDataset,
                          val_dataset: CharacterDataset,
                          model_params: Optional[Dict[str, Any]] = None,
                          training_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        训练单个模型
        
        Args:
            model_type (str): 模型类型
            train_dataset (CharacterDataset): 训练数据集
            val_dataset (CharacterDataset): 验证数据集
            model_params (Optional[Dict[str, Any]]): 模型参数
            training_params (Optional[Dict[str, Any]]): 训练参数
            
        Returns:
            Dict[str, Any]: 训练结果
        """
        self.logger.info(f"开始训练 {model_type.upper()} 模型...")
        
        # 合并参数
        model_params = model_params or {}
        training_params = training_params or {}
        
        # 创建模型
        model = self._create_model(model_type, model_params)
        
        # 创建数据加载器
        batch_size = training_params.get('batch_size', self.training_config.get('batch_size', 32))
        num_workers = training_params.get('num_workers', self.training_config.get('num_workers', 4))
        
        train_loader, val_loader = create_data_loaders(
            train_dataset, val_dataset, batch_size, num_workers
        )
        
        # 创建训练器
        temp_config = self.config.copy()
        temp_config['training'].update(training_params)
        
        trainer = ModelTrainer(model, temp_config)
        
        # 设置训练组件
        optimizer_type = training_params.get('optimizer', 'adam')
        criterion_type = training_params.get('criterion', 'crossentropy')
        scheduler_type = training_params.get('scheduler', 'steplr')
        
        trainer.setup_training(optimizer_type, criterion_type, scheduler_type)
        
        # 训练模型
        save_dir = None
        if self.experiment_dir:
            save_dir = str(Path(self.experiment_dir) / 'models' / model_type)
        
        start_time = time.time()
        history = trainer.train(train_loader, val_loader, save_dir)
        training_time = time.time() - start_time
        
        # 保存训练历史图表
        if self.experiment_dir:
            plot_path = Path(self.experiment_dir) / 'plots' / f'{model_type}_training_history.png'
            trainer.plot_training_history(str(plot_path))
        
        # 创建评估器并评估
        evaluator = ModelEvaluator(model, self.device)
        val_results = evaluator.evaluate_classification(val_loader)
        
        # 构建结果
        results = {
            'model_type': model_type,
            'model_params': model_params,
            'training_params': training_params,
            'training_history': history,
            'training_time': training_time,
            'best_val_accuracy': trainer.best_val_acc,
            'final_val_accuracy': val_results['overall_accuracy'],
            'model_parameters': model.count_parameters(),
            'validation_results': val_results
        }
        
        # 保存结果
        if self.experiment_dir:
            results_file = Path(self.experiment_dir) / 'logs' / f'{model_type}_results.json'
            with open(results_file, 'w', encoding='utf-8') as f:
                # 移除不能序列化的数据
                serializable_results = {k: v for k, v in results.items() 
                                      if k not in ['validation_results']}
                json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"{model_type.upper()} 模型训练完成，最佳验证准确率: {trainer.best_val_acc:.4f}")
        
        return results
    
    def train_multiple_models(self, model_types: List[str],
                            train_dataset: CharacterDataset,
                            val_dataset: CharacterDataset,
                            model_params_list: Optional[List[Dict[str, Any]]] = None,
                            training_params_list: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Dict[str, Any]]:
        """
        训练多个模型
        
        Args:
            model_types (List[str]): 模型类型列表
            train_dataset (CharacterDataset): 训练数据集
            val_dataset (CharacterDataset): 验证数据集
            model_params_list (Optional[List[Dict[str, Any]]]): 模型参数列表
            training_params_list (Optional[List[Dict[str, Any]]]): 训练参数列表
            
        Returns:
            Dict[str, Dict[str, Any]]: 所有模型的训练结果
        """
        all_results = {}
        
        # 确保参数列表长度匹配
        if model_params_list is None:
            model_params_list = [{}] * len(model_types)
        if training_params_list is None:
            training_params_list = [{}] * len(model_types)
        
        if len(model_params_list) != len(model_types):
            model_params_list = model_params_list[:len(model_types)] + [{}] * (len(model_types) - len(model_params_list))
        if len(training_params_list) != len(model_types):
            training_params_list = training_params_list[:len(model_types)] + [{}] * (len(model_types) - len(training_params_list))
        
        # 训练每个模型
        for i, model_type in enumerate(model_types):
            self.logger.info(f"训练模型 {i+1}/{len(model_types)}: {model_type}")
            
            try:
                results = self.train_single_model(
                    model_type,
                    train_dataset,
                    val_dataset,
                    model_params_list[i],
                    training_params_list[i]
                )
                all_results[model_type] = results
                
            except Exception as e:
                self.logger.error(f"训练 {model_type} 模型时出错: {str(e)}")
                all_results[model_type] = {'error': str(e)}
        
        # 保存比较结果
        if self.experiment_dir:
            self._save_model_comparison(all_results)
        
        return all_results
    
    def optimize_hyperparameters(self, model_type: str,
                                train_dataset: CharacterDataset,
                                val_dataset: CharacterDataset,
                                optimization_method: str = 'bayesian',
                                n_trials: int = 50) -> Dict[str, Any]:
        """
        优化超参数
        
        Args:
            model_type (str): 模型类型
            train_dataset (CharacterDataset): 训练数据集
            val_dataset (CharacterDataset): 验证数据集
            optimization_method (str): 优化方法
            n_trials (int): 试验次数
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        self.logger.info(f"开始 {model_type} 模型的超参数优化...")
        
        # 创建优化器
        optimizer = HyperparameterOptimizer(
            self.config, train_dataset, val_dataset, model_type
        )
        
        # 执行优化
        save_dir = None
        if self.experiment_dir:
            save_dir = str(Path(self.experiment_dir) / 'hyperparameter_optimization' / model_type)
        
        if optimization_method == 'bayesian':
            results = optimizer.bayesian_optimization(n_trials, save_dir)
        elif optimization_method == 'random':
            from .hyperparameter_optimizer import create_default_param_distributions
            param_distributions = create_default_param_distributions()
            results = optimizer.random_search(param_distributions, n_trials, save_dir)
        elif optimization_method == 'grid':
            from .hyperparameter_optimizer import create_default_param_grid
            param_grid = create_default_param_grid()
            results = optimizer.grid_search(param_grid, n_trials, save_dir)
        else:
            raise ValueError(f"不支持的优化方法: {optimization_method}")
        
        # 绘制优化历史
        if self.experiment_dir:
            plot_path = Path(self.experiment_dir) / 'plots' / f'{model_type}_optimization_history.png'
            optimizer.plot_optimization_history(str(plot_path))
        
        self.logger.info(f"{model_type} 超参数优化完成，最佳分数: {results['best_score']:.4f}")
        
        return results
    
    def _create_model(self, model_type: str, model_params: Dict[str, Any]) -> nn.Module:
        """创建模型"""
        base_params = {
            'num_classes': self.num_classes,
            **model_params
        }
        
        if model_type.lower() == 'cnn':
            model = CharacterRecognizer(**base_params)
        elif model_type.lower() == 'crnn':
            model = CharacterRecognizerCRNN(**base_params)
        elif model_type.lower() == 'transformer':
            model = CharacterRecognizerTransformer(**base_params)
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        return model.to(self.device)
    
    def _save_model_comparison(self, all_results: Dict[str, Dict[str, Any]]) -> None:
        """保存模型比较结果"""
        comparison_data = []
        
        for model_type, results in all_results.items():
            if 'error' not in results:
                comparison_data.append({
                    'model_type': model_type,
                    'best_val_accuracy': results.get('best_val_accuracy', 0.0),
                    'final_val_accuracy': results.get('final_val_accuracy', 0.0),
                    'training_time': results.get('training_time', 0.0),
                    'model_parameters': results.get('model_parameters', 0)
                })
        
        # 保存比较数据
        comparison_file = Path(self.experiment_dir) / 'logs' / 'model_comparison.json'
        with open(comparison_file, 'w', encoding='utf-8') as f:
            json.dump(comparison_data, f, ensure_ascii=False, indent=2)
        
        # 绘制比较图表
        self._plot_model_comparison(comparison_data)
    
    def _plot_model_comparison(self, comparison_data: List[Dict[str, Any]]) -> None:
        """绘制模型比较图表"""
        if not comparison_data:
            return
        
        import matplotlib.pyplot as plt
        
        model_types = [item['model_type'] for item in comparison_data]
        accuracies = [item['best_val_accuracy'] for item in comparison_data]
        training_times = [item['training_time'] for item in comparison_data]
        parameters = [item['model_parameters'] for item in comparison_data]
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 准确率比较
        axes[0, 0].bar(model_types, accuracies)
        axes[0, 0].set_title('模型准确率比较')
        axes[0, 0].set_ylabel('验证准确率')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 训练时间比较
        axes[0, 1].bar(model_types, training_times)
        axes[0, 1].set_title('训练时间比较')
        axes[0, 1].set_ylabel('训练时间 (秒)')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 参数数量比较
        axes[1, 0].bar(model_types, parameters)
        axes[1, 0].set_title('模型参数数量比较')
        axes[1, 0].set_ylabel('参数数量')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 效率比较（准确率/训练时间）
        efficiency = [acc / time if time > 0 else 0 for acc, time in zip(accuracies, training_times)]
        axes[1, 1].bar(model_types, efficiency)
        axes[1, 1].set_title('训练效率比较')
        axes[1, 1].set_ylabel('准确率/训练时间')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        # 保存图表
        plot_path = Path(self.experiment_dir) / 'plots' / 'model_comparison.png'
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"模型比较图表已保存: {plot_path}")
    
    def generate_experiment_report(self) -> str:
        """
        生成实验报告
        
        Returns:
            str: 报告内容
        """
        if not self.experiment_dir:
            return "没有活跃的实验"
        
        experiment_path = Path(self.experiment_dir)
        
        # 收集实验信息
        report_lines = [
            f"# 实验报告: {self.current_experiment}",
            f"实验目录: {self.experiment_dir}",
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## 实验配置"
        ]
        
        # 添加配置信息
        config_file = experiment_path / 'configs' / 'config.json'
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            report_lines.extend([
                f"- 字符类别数: {len(config.get('char_mapping', {}))}",
                f"- 训练epochs: {config.get('training', {}).get('epochs', 'N/A')}",
                f"- 学习率: {config.get('training', {}).get('learning_rate', 'N/A')}",
                f"- 批次大小: {config.get('training', {}).get('batch_size', 'N/A')}",
                ""
            ])
        
        # 添加数据集信息
        split_file = experiment_path / 'configs' / 'dataset_split.json'
        if split_file.exists():
            with open(split_file, 'r', encoding='utf-8') as f:
                split_info = json.load(f)
            
            report_lines.extend([
                "## 数据集信息",
                f"- 总样本数: {split_info['total_samples']}",
                f"- 训练样本: {split_info['train_samples']}",
                f"- 验证样本: {split_info['val_samples']}",
                f"- 测试样本: {split_info['test_samples']}",
                ""
            ])
        
        # 添加模型结果
        comparison_file = experiment_path / 'logs' / 'model_comparison.json'
        if comparison_file.exists():
            with open(comparison_file, 'r', encoding='utf-8') as f:
                comparison_data = json.load(f)
            
            report_lines.extend([
                "## 模型性能比较",
                "| 模型类型 | 最佳验证准确率 | 训练时间(秒) | 参数数量 |",
                "|---------|---------------|-------------|----------|"
            ])
            
            for item in comparison_data:
                report_lines.append(
                    f"| {item['model_type']} | {item['best_val_accuracy']:.4f} | "
                    f"{item['training_time']:.1f} | {item['model_parameters']:,} |"
                )
            
            report_lines.append("")
        
        # 保存报告
        report_content = "\n".join(report_lines)
        report_file = experiment_path / 'experiment_report.md'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        self.logger.info(f"实验报告已生成: {report_file}")
        
        return report_content
