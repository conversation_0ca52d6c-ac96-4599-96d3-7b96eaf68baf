#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像预处理器
Image Processor

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import cv2
import numpy as np
from pathlib import Path
from typing import Dict, Any, Tuple, List, Optional
import albumentations as A
from albumentations.pytorch import ToTensorV2

from ..utils.logger import LoggerMixin


class ImageProcessor(LoggerMixin):
    """图像预处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化处理器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.aug_config = config.get('augmentation', {})
        
        # 目标图像尺寸
        self.target_size = (224, 224)  # 可以从配置中读取
        
        # 初始化数据增强管道
        self._init_augmentation_pipelines()
        
        self.logger.info("图像预处理器初始化完成")
    
    def _init_augmentation_pipelines(self) -> None:
        """初始化数据增强管道"""
        
        # 训练时的数据增强
        self.train_transform = A.Compose([
            # 几何变换
            A.Rotate(
                limit=self.aug_config.get('rotation_range', 15),
                p=0.5
            ),
            A.ShiftScaleRotate(
                shift_limit=0.1,
                scale_limit=0.1,
                rotate_limit=10,
                p=0.5
            ),
            
            # 颜色变换
            A.RandomBrightnessContrast(
                brightness_limit=0.2,
                contrast_limit=0.2,
                p=0.5
            ),
            A.HueSaturationValue(
                hue_shift_limit=10,
                sat_shift_limit=20,
                val_shift_limit=20,
                p=0.3
            ),
            
            # 噪声和模糊
            A.GaussNoise(var_limit=(10.0, 50.0), p=0.3),
            A.GaussianBlur(blur_limit=(3, 7), p=0.2),
            A.MotionBlur(blur_limit=7, p=0.2),
            
            # 天气效果
            A.RandomRain(p=0.1),
            A.RandomFog(p=0.1),
            A.RandomSunFlare(p=0.1),
            
            # 标准化
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ])
        
        # 验证/测试时的变换
        self.val_transform = A.Compose([
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ])
        
        # 基础预处理（不包含增强）
        self.basic_transform = A.Compose([
            A.Resize(height=self.target_size[0], width=self.target_size[1]),
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            )
        ])
    
    def preprocess_image(self, image: np.ndarray, mode: str = 'basic') -> np.ndarray:
        """
        预处理单张图像
        
        Args:
            image (np.ndarray): 输入图像
            mode (str): 处理模式 ('train', 'val', 'basic')
            
        Returns:
            np.ndarray: 预处理后的图像
        """
        if mode == 'train':
            transformed = self.train_transform(image=image)
            return transformed['image']
        elif mode == 'val':
            transformed = self.val_transform(image=image)
            return transformed['image']
        else:  # basic
            transformed = self.basic_transform(image=image)
            return transformed['image']
    
    def enhance_image_quality(self, image: np.ndarray) -> np.ndarray:
        """
        增强图像质量
        
        Args:
            image (np.ndarray): 输入图像
            
        Returns:
            np.ndarray: 增强后的图像
        """
        # 去噪
        denoised = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
        
        # 锐化
        kernel = np.array([[-1,-1,-1],
                          [-1, 9,-1],
                          [-1,-1,-1]])
        sharpened = cv2.filter2D(denoised, -1, kernel)
        
        # 对比度增强
        lab = cv2.cvtColor(sharpened, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        # 应用CLAHE到L通道
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        l = clahe.apply(l)
        
        # 合并通道
        enhanced = cv2.merge([l, a, b])
        enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
        return enhanced
    
    def detect_and_correct_perspective(self, image: np.ndarray) -> Tuple[np.ndarray, bool]:
        """
        检测并矫正透视变形
        
        Args:
            image (np.ndarray): 输入图像
            
        Returns:
            Tuple[np.ndarray, bool]: 矫正后的图像和是否成功矫正的标志
        """
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 边缘检测
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)
            
            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 寻找最大的矩形轮廓
            largest_contour = None
            max_area = 0
            
            for contour in contours:
                # 近似轮廓
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # 如果是四边形且面积足够大
                if len(approx) == 4:
                    area = cv2.contourArea(contour)
                    if area > max_area:
                        max_area = area
                        largest_contour = approx
            
            if largest_contour is not None and max_area > 1000:
                # 获取四个角点
                pts = largest_contour.reshape(4, 2)
                
                # 排序角点：左上、右上、右下、左下
                rect = self._order_points(pts)
                
                # 计算目标矩形的尺寸
                (tl, tr, br, bl) = rect
                widthA = np.sqrt(((br[0] - bl[0]) ** 2) + ((br[1] - bl[1]) ** 2))
                widthB = np.sqrt(((tr[0] - tl[0]) ** 2) + ((tr[1] - tl[1]) ** 2))
                maxWidth = max(int(widthA), int(widthB))
                
                heightA = np.sqrt(((tr[0] - br[0]) ** 2) + ((tr[1] - br[1]) ** 2))
                heightB = np.sqrt(((tl[0] - bl[0]) ** 2) + ((tl[1] - bl[1]) ** 2))
                maxHeight = max(int(heightA), int(heightB))
                
                # 定义目标点
                dst = np.array([
                    [0, 0],
                    [maxWidth - 1, 0],
                    [maxWidth - 1, maxHeight - 1],
                    [0, maxHeight - 1]
                ], dtype="float32")
                
                # 计算透视变换矩阵
                M = cv2.getPerspectiveTransform(rect, dst)
                
                # 应用透视变换
                warped = cv2.warpPerspective(image, M, (maxWidth, maxHeight))
                
                return warped, True
            
        except Exception as e:
            self.logger.debug(f"透视矫正失败: {str(e)}")
        
        return image, False
    
    def _order_points(self, pts: np.ndarray) -> np.ndarray:
        """
        排序四个点：左上、右上、右下、左下
        
        Args:
            pts (np.ndarray): 四个点的坐标
            
        Returns:
            np.ndarray: 排序后的点坐标
        """
        rect = np.zeros((4, 2), dtype="float32")
        
        # 左上角的点具有最小的和，右下角的点具有最大的和
        s = pts.sum(axis=1)
        rect[0] = pts[np.argmin(s)]
        rect[2] = pts[np.argmax(s)]
        
        # 右上角的点具有最小的差，左下角的点具有最大的差
        diff = np.diff(pts, axis=1)
        rect[1] = pts[np.argmin(diff)]
        rect[3] = pts[np.argmax(diff)]
        
        return rect
    
    def normalize_image_size(self, image: np.ndarray, target_size: Tuple[int, int] = None) -> np.ndarray:
        """
        标准化图像尺寸
        
        Args:
            image (np.ndarray): 输入图像
            target_size (Tuple[int, int]): 目标尺寸，默认使用配置中的尺寸
            
        Returns:
            np.ndarray: 调整尺寸后的图像
        """
        if target_size is None:
            target_size = self.target_size
        
        # 保持宽高比的缩放
        h, w = image.shape[:2]
        target_h, target_w = target_size
        
        # 计算缩放比例
        scale = min(target_w / w, target_h / h)
        
        # 计算新的尺寸
        new_w = int(w * scale)
        new_h = int(h * scale)
        
        # 缩放图像
        resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
        
        # 创建目标尺寸的画布
        canvas = np.zeros((target_h, target_w, 3), dtype=np.uint8)
        
        # 计算居中位置
        y_offset = (target_h - new_h) // 2
        x_offset = (target_w - new_w) // 2
        
        # 将缩放后的图像放置在画布中心
        canvas[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized
        
        return canvas
    
    def validate_image(self, image: np.ndarray) -> Tuple[bool, str]:
        """
        验证图像质量
        
        Args:
            image (np.ndarray): 输入图像
            
        Returns:
            Tuple[bool, str]: 是否有效和错误信息
        """
        if image is None:
            return False, "图像为空"
        
        if len(image.shape) != 3:
            return False, "图像不是3通道彩色图像"
        
        h, w, c = image.shape
        
        if h < 50 or w < 50:
            return False, f"图像尺寸过小: {w}x{h}"
        
        if h > 5000 or w > 5000:
            return False, f"图像尺寸过大: {w}x{h}"
        
        # 检查图像是否过暗或过亮
        mean_brightness = np.mean(cv2.cvtColor(image, cv2.COLOR_BGR2GRAY))
        if mean_brightness < 20:
            return False, "图像过暗"
        if mean_brightness > 235:
            return False, "图像过亮"
        
        # 检查图像是否模糊
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        if laplacian_var < 100:
            return False, "图像过于模糊"
        
        return True, "图像有效"
