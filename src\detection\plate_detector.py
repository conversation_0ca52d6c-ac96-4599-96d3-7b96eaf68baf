#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车牌检测器
License Plate Detector

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import cv2
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
import torchvision.transforms as transforms

from ..utils.logger import LoggerMixin


class PlateDetector(LoggerMixin):
    """车牌检测器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化检测器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.detection_config = config.get('model', {}).get('detection', {})
        
        # 检测参数
        self.confidence_threshold = self.detection_config.get('confidence_threshold', 0.5)
        self.nms_threshold = self.detection_config.get('nms_threshold', 0.4)
        self.input_size = tuple(self.detection_config.get('input_size', [640, 640]))
        
        # 设备配置
        self.device = torch.device('cuda' if torch.cuda.is_available() and 
                                  config.get('hardware', {}).get('use_gpu', True) else 'cpu')
        
        self.model = None
        self.transform = self._create_transform()
        
        self.logger.info(f"车牌检测器初始化完成，使用设备: {self.device}")
    
    def _create_transform(self) -> transforms.Compose:
        """
        创建图像预处理变换
        
        Returns:
            transforms.Compose: 预处理变换
        """
        return transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize(self.input_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
    
    def load_model(self, model_path: str) -> bool:
        """
        加载预训练模型
        
        Args:
            model_path (str): 模型文件路径
            
        Returns:
            bool: 是否加载成功
        """
        try:
            if not Path(model_path).exists():
                self.logger.error(f"模型文件不存在: {model_path}")
                return False
            
            self.model = torch.load(model_path, map_location=self.device)
            self.model.eval()
            self.logger.info(f"成功加载模型: {model_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载模型失败: {str(e)}")
            return False
    
    def detect(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        检测车牌

        Args:
            image (np.ndarray): 输入图像

        Returns:
            List[Dict[str, Any]]: 检测结果列表
        """
        if self.model is None:
            self.logger.error("模型未加载")
            return []

        try:
            # 预处理图像
            processed_image = self._preprocess_image(image)

            # 模型推理
            with torch.no_grad():
                predictions = self.model(processed_image.unsqueeze(0).to(self.device))

            # 后处理
            detections = self._postprocess_predictions(predictions, image.shape)

            return detections

        except Exception as e:
            self.logger.error(f"检测失败: {str(e)}")
            return []
    
    def _preprocess_image(self, image: np.ndarray) -> torch.Tensor:
        """
        预处理图像
        
        Args:
            image (np.ndarray): 输入图像
            
        Returns:
            torch.Tensor: 预处理后的张量
        """
        # 确保图像是RGB格式
        if len(image.shape) == 3 and image.shape[2] == 3:
            # BGR to RGB
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 应用变换
        tensor = self.transform(image)
        return tensor
    
    def _postprocess_predictions(self, predictions: torch.Tensor, 
                                original_shape: Tuple[int, int, int]) -> List[Dict[str, Any]]:
        """
        后处理预测结果
        
        Args:
            predictions (torch.Tensor): 模型预测结果
            original_shape (Tuple[int, int, int]): 原始图像形状
            
        Returns:
            List[Dict[str, Any]]: 处理后的检测结果
        """
        # 这里需要根据具体的模型输出格式来实现
        # 目前返回一个示例结果
        detections = []
        
        # 示例：假设检测到一个车牌
        if len(predictions) > 0:
            detection = {
                'bbox': [100, 100, 300, 150],  # [x1, y1, x2, y2]
                'confidence': 0.9,
                'class': 'license_plate'
            }
            detections.append(detection)
        
        return detections
    
    def visualize_detections(self, image: np.ndarray, 
                           detections: List[Dict[str, Any]]) -> np.ndarray:
        """
        可视化检测结果
        
        Args:
            image (np.ndarray): 原始图像
            detections (List[Dict[str, Any]]): 检测结果
            
        Returns:
            np.ndarray: 带有检测框的图像
        """
        result_image = image.copy()
        
        for detection in detections:
            bbox = detection['bbox']
            confidence = detection['confidence']
            
            # 绘制边界框
            cv2.rectangle(result_image, 
                         (int(bbox[0]), int(bbox[1])), 
                         (int(bbox[2]), int(bbox[3])), 
                         (0, 255, 0), 2)
            
            # 绘制置信度
            label = f"Plate: {confidence:.2f}"
            cv2.putText(result_image, label, 
                       (int(bbox[0]), int(bbox[1]) - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        return result_image
    
    def extract_plate_regions(self, image: np.ndarray, 
                            detections: List[Dict[str, Any]]) -> List[np.ndarray]:
        """
        提取车牌区域
        
        Args:
            image (np.ndarray): 原始图像
            detections (List[Dict[str, Any]]): 检测结果
            
        Returns:
            List[np.ndarray]: 车牌区域图像列表
        """
        plate_regions = []
        
        for detection in detections:
            bbox = detection['bbox']
            x1, y1, x2, y2 = map(int, bbox)
            
            # 确保边界框在图像范围内
            h, w = image.shape[:2]
            x1 = max(0, x1)
            y1 = max(0, y1)
            x2 = min(w, x2)
            y2 = min(h, y2)
            
            # 提取区域
            plate_region = image[y1:y2, x1:x2]
            if plate_region.size > 0:
                plate_regions.append(plate_region)
        
        return plate_regions


class SimpleDetector(PlateDetector):
    """简单的车牌检测器实现"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化简单检测器

        Args:
            config (Dict[str, Any]): 配置字典
        """
        super().__init__(config)
        # 简单检测器不需要加载模型
        self.model = "simple_cv_detector"  # 标记为已"加载"
        self.logger.info("使用简单检测器（基于传统计算机视觉方法）")
    
    def detect(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        使用传统计算机视觉方法检测车牌
        
        Args:
            image (np.ndarray): 输入图像
            
        Returns:
            List[Dict[str, Any]]: 检测结果列表
        """
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 边缘检测
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)
            
            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            detections = []
            
            for contour in contours:
                # 计算轮廓的边界矩形
                x, y, w, h = cv2.boundingRect(contour)
                
                # 过滤条件：车牌的宽高比通常在2-5之间
                aspect_ratio = w / h if h > 0 else 0
                area = w * h
                
                if (2.0 <= aspect_ratio <= 5.0 and 
                    area > 1000 and 
                    w > 50 and h > 20):
                    
                    detection = {
                        'bbox': [x, y, x + w, y + h],
                        'confidence': min(0.9, area / 10000),  # 简单的置信度计算
                        'class': 'license_plate'
                    }
                    detections.append(detection)
            
            # 按置信度排序，返回最好的几个结果
            detections.sort(key=lambda x: x['confidence'], reverse=True)
            return detections[:3]  # 最多返回3个检测结果
            
        except Exception as e:
            self.logger.error(f"简单检测失败: {str(e)}")
            return []


def create_detector(config: Dict[str, Any], detector_type: str = 'simple') -> PlateDetector:
    """
    创建车牌检测器
    
    Args:
        config (Dict[str, Any]): 配置字典
        detector_type (str): 检测器类型
        
    Returns:
        PlateDetector: 检测器实例
    """
    if detector_type == 'simple':
        return SimpleDetector(config)
    else:
        return PlateDetector(config)
