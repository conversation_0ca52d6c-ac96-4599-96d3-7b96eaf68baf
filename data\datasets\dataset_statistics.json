{"dataset_info": {"total_samples": 100, "train_samples": 80, "val_samples": 10, "test_samples": 10, "character_classes": 66, "train_ratio": 0.8, "val_ratio": 0.1, "test_ratio": 0.1}, "generation_results": {"train": {"split_name": "train", "total_samples": 80, "success_count": 80, "error_count": 0, "dataset_path": "data\\datasets\\train"}, "val": {"split_name": "val", "total_samples": 10, "success_count": 10, "error_count": 0, "dataset_path": "data\\datasets\\val"}, "test": {"split_name": "test", "total_samples": 10, "success_count": 10, "error_count": 0, "dataset_path": "data\\datasets\\test"}}, "character_mapping_info": {"total_characters": 66, "provinces": 31, "letters": 24, "digits": 10}}