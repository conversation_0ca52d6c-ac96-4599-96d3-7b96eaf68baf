#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据集生成问题
"""

from pathlib import Path
from src.utils.config_loader import load_config

def debug_data_collection():
    """调试数据收集过程"""
    config = load_config('config/config.yaml')
    
    # 检查路径配置
    processed_data_path = Path(config['data']['processed_data_path'])
    raw_data_path = Path(config['data']['raw_data_path'])
    
    print(f"处理后数据路径: {processed_data_path}")
    print(f"原始数据路径: {raw_data_path}")
    print(f"处理后数据路径存在: {processed_data_path.exists()}")
    print(f"原始数据路径存在: {raw_data_path.exists()}")
    
    print("\n=== 处理后数据目录内容 ===")
    if processed_data_path.exists():
        for item in processed_data_path.iterdir():
            print(f"  {item.name} ({'目录' if item.is_dir() else '文件'})")
            if item.is_dir():
                jpg_files = list(item.glob('*.jpg'))
                json_files = list(item.glob('*.json'))
                print(f"    - JPG文件: {len(jpg_files)}")
                print(f"    - JSON文件: {len(json_files)}")
                if jpg_files:
                    print(f"    - 第一个JPG: {jpg_files[0].name}")
                if json_files:
                    print(f"    - 第一个JSON: {json_files[0].name}")
    
    print("\n=== 原始数据目录内容 ===")
    if raw_data_path.exists():
        for item in raw_data_path.iterdir():
            print(f"  {item.name} ({'目录' if item.is_dir() else '文件'})")
            if item.is_dir():
                annotations_dir = item / 'annotations'
                print(f"    - annotations目录存在: {annotations_dir.exists()}")
                if annotations_dir.exists():
                    json_files = list(annotations_dir.glob('*.json'))
                    print(f"    - 标注文件数量: {len(json_files)}")
                    if json_files:
                        print(f"    - 第一个标注文件: {json_files[0].name}")

if __name__ == "__main__":
    debug_data_collection()
