# 基于深度学习的车牌识别系统

## 项目简介

本项目是一个基于深度学习技术的车牌识别系统，使用PyTorch框架实现。系统能够自动检测和识别图像中的车牌信息，支持多种车牌类型，具有较高的识别准确率和良好的鲁棒性。

## 功能特性

- 🚗 **车牌检测**: 自动定位图像中的车牌区域
- 🔤 **字符识别**: 准确识别车牌上的字符信息
- 🎨 **多类型支持**: 支持蓝牌、黄牌、绿牌等多种车牌类型
- 🌟 **高准确率**: 识别准确率≥95%
- ⚡ **实时处理**: 单张图像处理时间≤1秒
- 🖥️ **友好界面**: 提供直观的图形用户界面

## 项目结构

```
基于深度学习的车牌识别系统/
├── src/                    # 源代码目录
│   ├── data_collection/    # 数据采集模块
│   ├── data_preprocessing/ # 数据预处理模块
│   ├── models/            # 深度学习模型
│   ├── detection/         # 车牌检测模块
│   ├── recognition/       # 字符识别模块
│   ├── utils/             # 工具函数
│   └── gui/               # 图形用户界面
├── data/                  # 数据目录
│   ├── raw/               # 原始数据
│   ├── processed/         # 处理后数据
│   └── datasets/          # 训练数据集
├── models/                # 模型文件
│   ├── saved_models/      # 训练好的模型
│   └── checkpoints/       # 训练检查点
├── config/                # 配置文件
├── tests/                 # 测试代码
├── logs/                  # 日志文件
├── requirements.txt       # 依赖包列表
├── main.py               # 主程序入口
└── README.md             # 项目说明
```

## 安装说明

### 环境要求

- Python 3.7+
- PyTorch 1.8+
- CUDA支持的GPU（推荐）

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd 基于深度学习的车牌识别系统
```

2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 数据采集
```bash
python main.py --mode collect
```

### 2. 模型训练
```bash
python main.py --mode train --config config/config.yaml
```

### 3. 模型测试
```bash
python main.py --mode test --input path/to/test/image.jpg
```

### 4. 图形界面演示
```bash
python main.py --mode demo
```

## 配置说明

系统配置文件位于 `config/config.yaml`，包含以下主要配置项：

- **数据配置**: 数据路径、训练/验证/测试比例
- **模型配置**: 模型参数、网络结构设置
- **训练配置**: 学习率、批次大小、训练轮数等
- **硬件配置**: GPU使用设置

## 性能指标

- **识别准确率**: ≥95%
- **处理速度**: ≤1秒/张
- **支持格式**: JPEG, PNG, BMP
- **适用场景**: 各种光照条件、拍摄角度

## 开发计划

- [x] 项目环境初始化
- [ ] 数据采集模块开发
- [ ] 数据预处理模块开发
- [ ] 车牌检测模型设计
- [ ] 字符识别模型开发
- [ ] 模型训练与优化
- [ ] 系统集成与测试
- [ ] 用户界面开发

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 许可证

本项目采用MIT许可证。

## 联系方式

如有问题或建议，请联系项目维护者。
