#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件加载器
Configuration Loader

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import yaml
import os
from pathlib import Path
from typing import Dict, Any


def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载YAML配置文件
    
    Args:
        config_path (str): 配置文件路径
        
    Returns:
        Dict[str, Any]: 配置字典
        
    Raises:
        FileNotFoundError: 配置文件不存在
        yaml.YAMLError: YAML格式错误
    """
    config_path = Path(config_path)
    
    if not config_path.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 验证配置文件结构
        _validate_config(config)
        
        # 处理相对路径
        config = _process_paths(config, config_path.parent)
        
        return config
        
    except yaml.YAMLError as e:
        raise yaml.YAMLError(f"配置文件格式错误: {e}")


def _validate_config(config: Dict[str, Any]) -> None:
    """
    验证配置文件结构
    
    Args:
        config (Dict[str, Any]): 配置字典
        
    Raises:
        ValueError: 配置结构不正确
    """
    required_sections = ['data', 'model', 'training', 'hardware']
    
    for section in required_sections:
        if section not in config:
            raise ValueError(f"配置文件缺少必要部分: {section}")


def _process_paths(config: Dict[str, Any], config_dir: Path) -> Dict[str, Any]:
    """
    处理配置中的相对路径，转换为绝对路径
    
    Args:
        config (Dict[str, Any]): 配置字典
        config_dir (Path): 配置文件所在目录
        
    Returns:
        Dict[str, Any]: 处理后的配置字典
    """
    # 获取项目根目录
    project_root = config_dir.parent
    
    # 处理数据路径
    if 'data' in config:
        for key in ['raw_data_path', 'processed_data_path', 'dataset_path']:
            if key in config['data']:
                path = Path(config['data'][key])
                if not path.is_absolute():
                    config['data'][key] = str(project_root / path)
    
    # 处理模型路径
    if 'output' in config:
        for key in ['model_save_path', 'checkpoint_path', 'results_path']:
            if key in config['output']:
                path = Path(config['output'][key])
                if not path.is_absolute():
                    config['output'][key] = str(project_root / path)
    
    # 处理日志路径
    if 'logging' in config and 'log_file' in config['logging']:
        path = Path(config['logging']['log_file'])
        if not path.is_absolute():
            config['logging']['log_file'] = str(project_root / path)
    
    return config


def save_config(config: Dict[str, Any], config_path: str) -> None:
    """
    保存配置到YAML文件
    
    Args:
        config (Dict[str, Any]): 配置字典
        config_path (str): 配置文件路径
    """
    config_path = Path(config_path)
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)


def get_config_value(config: Dict[str, Any], key_path: str, default=None):
    """
    获取嵌套配置值
    
    Args:
        config (Dict[str, Any]): 配置字典
        key_path (str): 键路径，用点分隔，如 'model.detection.input_size'
        default: 默认值
        
    Returns:
        配置值或默认值
    """
    keys = key_path.split('.')
    value = config
    
    try:
        for key in keys:
            value = value[key]
        return value
    except (KeyError, TypeError):
        return default
