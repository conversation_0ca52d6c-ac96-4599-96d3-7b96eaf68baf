# 车牌识别系统配置文件

# 数据配置
data:
  raw_data_path: "data/raw"
  processed_data_path: "data/processed"
  dataset_path: "data/datasets"
  train_ratio: 0.8
  val_ratio: 0.1
  test_ratio: 0.1

# 模型配置
model:
  # 车牌检测模型
  detection:
    model_name: "yolo_v5"
    input_size: [640, 640]
    confidence_threshold: 0.5
    nms_threshold: 0.4
  
  # 字符识别模型
  recognition:
    model_name: "resnet18"
    input_size: [64, 64]
    num_classes: 65  # 中文字符 + 数字 + 字母
    dropout_rate: 0.5

# 训练配置
training:
  batch_size: 32
  learning_rate: 0.001
  num_epochs: 100
  early_stopping_patience: 10
  save_best_only: true
  
# 数据增强配置
augmentation:
  rotation_range: 15
  width_shift_range: 0.1
  height_shift_range: 0.1
  brightness_range: [0.8, 1.2]
  zoom_range: 0.1
  horizontal_flip: false

# 硬件配置
hardware:
  use_gpu: true
  gpu_id: 0
  num_workers: 4

# 日志配置
logging:
  level: "INFO"
  log_file: "logs/training.log"
  
# 输出配置
output:
  model_save_path: "models/saved_models"
  checkpoint_path: "models/checkpoints"
  results_path: "results"
