#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型训练器
Model Trainer

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from typing import Dict, Any, List, Tuple, Optional, Callable
from pathlib import Path
import time
from tqdm import tqdm
import matplotlib.pyplot as plt

from ..utils.logger import LoggerMixin


class ModelTrainer(LoggerMixin):
    """模型训练器"""
    
    def __init__(self, model: nn.Module, config: Dict[str, Any]):
        """
        初始化模型训练器
        
        Args:
            model (nn.Module): 要训练的模型
            config (Dict[str, Any]): 配置字典
        """
        self.model = model
        self.config = config
        self.train_config = config.get('training', {})
        
        # 训练参数
        self.epochs = self.train_config.get('epochs', 100)
        self.learning_rate = self.train_config.get('learning_rate', 0.001)
        self.batch_size = self.train_config.get('batch_size', 32)
        self.weight_decay = self.train_config.get('weight_decay', 1e-4)
        
        # 设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)
        
        # 优化器和损失函数
        self.optimizer = None
        self.criterion = None
        self.scheduler = None
        
        # 训练历史
        self.train_history = {
            'train_loss': [],
            'train_acc': [],
            'val_loss': [],
            'val_acc': [],
            'learning_rates': []
        }
        
        # 最佳模型状态
        self.best_val_acc = 0.0
        self.best_model_state = None
        
        self.logger.info(f"模型训练器初始化完成，使用设备: {self.device}")
    
    def setup_training(self, optimizer_type: str = 'adam',
                      criterion_type: str = 'crossentropy',
                      scheduler_type: Optional[str] = 'steplr') -> None:
        """
        设置训练组件
        
        Args:
            optimizer_type (str): 优化器类型
            criterion_type (str): 损失函数类型
            scheduler_type (Optional[str]): 学习率调度器类型
        """
        # 设置优化器
        if optimizer_type.lower() == 'adam':
            self.optimizer = optim.Adam(
                self.model.parameters(),
                lr=self.learning_rate,
                weight_decay=self.weight_decay
            )
        elif optimizer_type.lower() == 'sgd':
            self.optimizer = optim.SGD(
                self.model.parameters(),
                lr=self.learning_rate,
                momentum=0.9,
                weight_decay=self.weight_decay
            )
        elif optimizer_type.lower() == 'adamw':
            self.optimizer = optim.AdamW(
                self.model.parameters(),
                lr=self.learning_rate,
                weight_decay=self.weight_decay
            )
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_type}")
        
        # 设置损失函数
        if criterion_type.lower() == 'crossentropy':
            self.criterion = nn.CrossEntropyLoss()
        elif criterion_type.lower() == 'focalloss':
            self.criterion = self._create_focal_loss()
        elif criterion_type.lower() == 'labelsmoothingce':
            self.criterion = self._create_label_smoothing_ce()
        else:
            raise ValueError(f"不支持的损失函数类型: {criterion_type}")
        
        # 设置学习率调度器
        if scheduler_type:
            if scheduler_type.lower() == 'steplr':
                self.scheduler = optim.lr_scheduler.StepLR(
                    self.optimizer, step_size=30, gamma=0.1
                )
            elif scheduler_type.lower() == 'cosineannealinglr':
                self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                    self.optimizer, T_max=self.epochs
                )
            elif scheduler_type.lower() == 'reducelronplateau':
                self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                    self.optimizer, mode='max', factor=0.5, patience=10
                )
        
        self.logger.info(f"训练组件设置完成: {optimizer_type}, {criterion_type}, {scheduler_type}")
    
    def train(self, train_loader: DataLoader, 
              val_loader: Optional[DataLoader] = None,
              save_dir: Optional[str] = None) -> Dict[str, List[float]]:
        """
        训练模型
        
        Args:
            train_loader (DataLoader): 训练数据加载器
            val_loader (Optional[DataLoader]): 验证数据加载器
            save_dir (Optional[str]): 模型保存目录
            
        Returns:
            Dict[str, List[float]]: 训练历史
        """
        if self.optimizer is None or self.criterion is None:
            self.setup_training()
        
        self.logger.info(f"开始训练，共 {self.epochs} 个epoch")
        
        start_time = time.time()
        
        for epoch in range(self.epochs):
            epoch_start_time = time.time()
            
            # 训练阶段
            train_loss, train_acc = self._train_epoch(train_loader, epoch)
            
            # 验证阶段
            val_loss, val_acc = 0.0, 0.0
            if val_loader is not None:
                val_loss, val_acc = self._validate_epoch(val_loader, epoch)
            
            # 更新学习率
            if self.scheduler is not None:
                if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                    self.scheduler.step(val_acc if val_loader else train_acc)
                else:
                    self.scheduler.step()
            
            # 记录历史
            self.train_history['train_loss'].append(train_loss)
            self.train_history['train_acc'].append(train_acc)
            self.train_history['val_loss'].append(val_loss)
            self.train_history['val_acc'].append(val_acc)
            self.train_history['learning_rates'].append(
                self.optimizer.param_groups[0]['lr']
            )
            
            # 保存最佳模型
            if val_loader is not None and val_acc > self.best_val_acc:
                self.best_val_acc = val_acc
                self.best_model_state = self.model.state_dict().copy()
                
                if save_dir:
                    self._save_checkpoint(save_dir, epoch, val_acc, is_best=True)
            
            # 定期保存检查点
            if save_dir and (epoch + 1) % 10 == 0:
                self._save_checkpoint(save_dir, epoch, val_acc if val_loader else train_acc)
            
            # 打印进度
            epoch_time = time.time() - epoch_start_time
            self.logger.info(
                f"Epoch {epoch+1}/{self.epochs} - "
                f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f} - "
                f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f} - "
                f"LR: {self.optimizer.param_groups[0]['lr']:.6f} - "
                f"Time: {epoch_time:.2f}s"
            )
        
        total_time = time.time() - start_time
        self.logger.info(f"训练完成，总用时: {total_time:.2f}s")
        
        # 加载最佳模型
        if self.best_model_state is not None:
            self.model.load_state_dict(self.best_model_state)
            self.logger.info(f"已加载最佳模型，验证准确率: {self.best_val_acc:.4f}")
        
        return self.train_history
    
    def _train_epoch(self, train_loader: DataLoader, epoch: int) -> Tuple[float, float]:
        """
        训练一个epoch
        
        Args:
            train_loader (DataLoader): 训练数据加载器
            epoch (int): 当前epoch
            
        Returns:
            Tuple[float, float]: 平均损失和准确率
        """
        self.model.train()
        
        total_loss = 0.0
        total_correct = 0
        total_samples = 0
        
        progress_bar = tqdm(train_loader, desc=f'Epoch {epoch+1} Training')
        
        for batch_idx, (data, targets) in enumerate(progress_bar):
            data, targets = data.to(self.device), targets.to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            outputs = self.model(data)
            loss = self.criterion(outputs, targets)
            
            # 反向传播
            loss.backward()
            self.optimizer.step()
            
            # 统计
            total_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total_samples += targets.size(0)
            total_correct += (predicted == targets).sum().item()
            
            # 更新进度条
            avg_loss = total_loss / (batch_idx + 1)
            avg_acc = total_correct / total_samples
            progress_bar.set_postfix({
                'Loss': f'{avg_loss:.4f}',
                'Acc': f'{avg_acc:.4f}'
            })
        
        return total_loss / len(train_loader), total_correct / total_samples
    
    def _validate_epoch(self, val_loader: DataLoader, epoch: int) -> Tuple[float, float]:
        """
        验证一个epoch
        
        Args:
            val_loader (DataLoader): 验证数据加载器
            epoch (int): 当前epoch
            
        Returns:
            Tuple[float, float]: 平均损失和准确率
        """
        self.model.eval()
        
        total_loss = 0.0
        total_correct = 0
        total_samples = 0
        
        with torch.no_grad():
            progress_bar = tqdm(val_loader, desc=f'Epoch {epoch+1} Validation')
            
            for data, targets in progress_bar:
                data, targets = data.to(self.device), targets.to(self.device)
                
                # 前向传播
                outputs = self.model(data)
                loss = self.criterion(outputs, targets)
                
                # 统计
                total_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total_samples += targets.size(0)
                total_correct += (predicted == targets).sum().item()
                
                # 更新进度条
                avg_loss = total_loss / len(progress_bar)
                avg_acc = total_correct / total_samples
                progress_bar.set_postfix({
                    'Loss': f'{avg_loss:.4f}',
                    'Acc': f'{avg_acc:.4f}'
                })
        
        return total_loss / len(val_loader), total_correct / total_samples
    
    def evaluate(self, test_loader: DataLoader) -> Dict[str, float]:
        """
        评估模型
        
        Args:
            test_loader (DataLoader): 测试数据加载器
            
        Returns:
            Dict[str, float]: 评估指标
        """
        self.model.eval()
        
        total_loss = 0.0
        total_correct = 0
        total_samples = 0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for data, targets in tqdm(test_loader, desc='Evaluating'):
                data, targets = data.to(self.device), targets.to(self.device)
                
                outputs = self.model(data)
                loss = self.criterion(outputs, targets)
                
                total_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total_samples += targets.size(0)
                total_correct += (predicted == targets).sum().item()
                
                all_predictions.extend(predicted.cpu().numpy())
                all_targets.extend(targets.cpu().numpy())
        
        # 计算指标
        accuracy = total_correct / total_samples
        avg_loss = total_loss / len(test_loader)
        
        # 计算每类准确率
        class_accuracies = self._calculate_class_accuracies(all_predictions, all_targets)
        
        metrics = {
            'accuracy': accuracy,
            'loss': avg_loss,
            'class_accuracies': class_accuracies
        }
        
        self.logger.info(f"评估完成 - 准确率: {accuracy:.4f}, 损失: {avg_loss:.4f}")
        
        return metrics
    
    def _calculate_class_accuracies(self, predictions: List[int], 
                                  targets: List[int]) -> Dict[int, float]:
        """
        计算每类准确率
        
        Args:
            predictions (List[int]): 预测结果
            targets (List[int]): 真实标签
            
        Returns:
            Dict[int, float]: 每类准确率
        """
        class_correct = {}
        class_total = {}
        
        for pred, target in zip(predictions, targets):
            if target not in class_total:
                class_total[target] = 0
                class_correct[target] = 0
            
            class_total[target] += 1
            if pred == target:
                class_correct[target] += 1
        
        class_accuracies = {}
        for class_id in class_total:
            class_accuracies[class_id] = class_correct[class_id] / class_total[class_id]
        
        return class_accuracies
    
    def _save_checkpoint(self, save_dir: str, epoch: int, 
                        accuracy: float, is_best: bool = False) -> None:
        """
        保存检查点
        
        Args:
            save_dir (str): 保存目录
            epoch (int): 当前epoch
            accuracy (float): 准确率
            is_best (bool): 是否为最佳模型
        """
        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)
        
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'accuracy': accuracy,
            'train_history': self.train_history,
            'config': self.config
        }
        
        if self.scheduler is not None:
            checkpoint['scheduler_state_dict'] = self.scheduler.state_dict()
        
        # 保存当前检查点
        checkpoint_path = save_path / f'checkpoint_epoch_{epoch+1}.pth'
        torch.save(checkpoint, checkpoint_path)
        
        # 保存最佳模型
        if is_best:
            best_path = save_path / 'best_model.pth'
            torch.save(checkpoint, best_path)
            self.logger.info(f"保存最佳模型: {best_path}")
    
    def _create_focal_loss(self, alpha: float = 1.0, gamma: float = 2.0) -> nn.Module:
        """创建Focal Loss"""
        class FocalLoss(nn.Module):
            def __init__(self, alpha=alpha, gamma=gamma):
                super(FocalLoss, self).__init__()
                self.alpha = alpha
                self.gamma = gamma
                self.ce_loss = nn.CrossEntropyLoss(reduction='none')
            
            def forward(self, inputs, targets):
                ce_loss = self.ce_loss(inputs, targets)
                pt = torch.exp(-ce_loss)
                focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
                return focal_loss.mean()
        
        return FocalLoss()
    
    def _create_label_smoothing_ce(self, smoothing: float = 0.1) -> nn.Module:
        """创建Label Smoothing CrossEntropy Loss"""
        class LabelSmoothingCrossEntropy(nn.Module):
            def __init__(self, smoothing=smoothing):
                super(LabelSmoothingCrossEntropy, self).__init__()
                self.smoothing = smoothing
            
            def forward(self, inputs, targets):
                log_probs = torch.log_softmax(inputs, dim=-1)
                nll_loss = -log_probs.gather(dim=-1, index=targets.unsqueeze(1))
                nll_loss = nll_loss.squeeze(1)
                smooth_loss = -log_probs.mean(dim=-1)
                loss = (1 - self.smoothing) * nll_loss + self.smoothing * smooth_loss
                return loss.mean()
        
        return LabelSmoothingCrossEntropy()
    
    def plot_training_history(self, save_path: Optional[str] = None) -> None:
        """
        绘制训练历史
        
        Args:
            save_path (Optional[str]): 保存路径
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        
        # 损失曲线
        axes[0, 0].plot(self.train_history['train_loss'], label='Train Loss')
        if self.train_history['val_loss']:
            axes[0, 0].plot(self.train_history['val_loss'], label='Val Loss')
        axes[0, 0].set_title('Loss')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 准确率曲线
        axes[0, 1].plot(self.train_history['train_acc'], label='Train Acc')
        if self.train_history['val_acc']:
            axes[0, 1].plot(self.train_history['val_acc'], label='Val Acc')
        axes[0, 1].set_title('Accuracy')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Accuracy')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # 学习率曲线
        axes[1, 0].plot(self.train_history['learning_rates'])
        axes[1, 0].set_title('Learning Rate')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Learning Rate')
        axes[1, 0].grid(True)
        
        # 训练统计
        axes[1, 1].text(0.1, 0.8, f"Best Val Acc: {self.best_val_acc:.4f}", 
                       transform=axes[1, 1].transAxes, fontsize=12)
        axes[1, 1].text(0.1, 0.6, f"Total Epochs: {len(self.train_history['train_loss'])}", 
                       transform=axes[1, 1].transAxes, fontsize=12)
        axes[1, 1].text(0.1, 0.4, f"Final Train Acc: {self.train_history['train_acc'][-1]:.4f}", 
                       transform=axes[1, 1].transAxes, fontsize=12)
        axes[1, 1].set_title('Training Statistics')
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"训练历史图表已保存: {save_path}")
        
        plt.show()
