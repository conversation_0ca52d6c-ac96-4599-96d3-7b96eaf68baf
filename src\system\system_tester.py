#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试器
System Tester

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import cv2
import numpy as np
import json
import time
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path
import matplotlib.pyplot as plt
from collections import defaultdict
import pandas as pd

from ..utils.logger import LoggerMixin
from .license_plate_recognizer import LicensePlateRecognizer


class SystemTester(LoggerMixin):
    """系统测试器"""
    
    def __init__(self, recognizer: LicensePlateRecognizer):
        """
        初始化系统测试器
        
        Args:
            recognizer (LicensePlateRecognizer): 车牌识别系统
        """
        self.recognizer = recognizer
        self.test_results = []
        self.performance_metrics = {}
        
        self.logger.info("系统测试器初始化完成")
    
    def run_accuracy_test(self, test_data_dir: str, 
                         ground_truth_file: str) -> Dict[str, Any]:
        """
        运行准确率测试
        
        Args:
            test_data_dir (str): 测试数据目录
            ground_truth_file (str): 真实标签文件
            
        Returns:
            Dict[str, Any]: 测试结果
        """
        self.logger.info("开始准确率测试...")
        
        # 加载真实标签
        ground_truth = self._load_ground_truth(ground_truth_file)
        
        # 获取测试图像
        test_images = list(Path(test_data_dir).glob('*.jpg')) + \
                     list(Path(test_data_dir).glob('*.png'))
        
        if not test_images:
            raise ValueError(f"在 {test_data_dir} 中未找到测试图像")
        
        # 测试每张图像
        correct_predictions = 0
        total_predictions = 0
        detailed_results = []
        
        for image_path in test_images:
            image_name = image_path.name
            
            if image_name not in ground_truth:
                self.logger.warning(f"图像 {image_name} 没有真实标签，跳过")
                continue
            
            true_label = ground_truth[image_name]
            
            # 识别车牌
            start_time = time.time()
            result = self.recognizer.recognize_from_image(str(image_path), return_details=True)
            processing_time = time.time() - start_time
            
            predicted_label = result.get('license_plate', '')
            confidence = result.get('confidence', 0.0)
            
            # 判断是否正确
            is_correct = predicted_label == true_label
            if is_correct:
                correct_predictions += 1
            
            total_predictions += 1
            
            # 记录详细结果
            detailed_result = {
                'image_name': image_name,
                'true_label': true_label,
                'predicted_label': predicted_label,
                'confidence': confidence,
                'is_correct': is_correct,
                'processing_time': processing_time,
                'error': result.get('error', '')
            }
            detailed_results.append(detailed_result)
            
            if total_predictions % 10 == 0:
                self.logger.info(f"已测试 {total_predictions}/{len(test_images)} 张图像")
        
        # 计算准确率
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0.0
        
        # 分析错误类型
        error_analysis = self._analyze_errors(detailed_results)
        
        # 计算置信度统计
        confidence_stats = self._analyze_confidence(detailed_results)
        
        test_result = {
            'total_images': total_predictions,
            'correct_predictions': correct_predictions,
            'accuracy': accuracy,
            'error_analysis': error_analysis,
            'confidence_stats': confidence_stats,
            'detailed_results': detailed_results
        }
        
        self.test_results.append(test_result)
        
        self.logger.info(f"准确率测试完成，准确率: {accuracy:.4f}")
        
        return test_result
    
    def run_performance_test(self, test_images: List[str],
                           num_iterations: int = 100) -> Dict[str, Any]:
        """
        运行性能测试
        
        Args:
            test_images (List[str]): 测试图像路径列表
            num_iterations (int): 迭代次数
            
        Returns:
            Dict[str, Any]: 性能测试结果
        """
        self.logger.info(f"开始性能测试，迭代次数: {num_iterations}")
        
        processing_times = []
        detection_times = []
        segmentation_times = []
        recognition_times = []
        
        # 重置性能统计
        self.recognizer.reset_performance_stats()
        
        for i in range(num_iterations):
            # 随机选择测试图像
            image_path = np.random.choice(test_images)
            
            # 识别车牌
            start_time = time.time()
            result = self.recognizer.recognize_from_image(image_path, return_details=True)
            total_time = time.time() - start_time
            
            processing_times.append(total_time)
            
            # 记录各阶段时间
            if 'processing_times' in result:
                times = result['processing_times']
                detection_times.append(times.get('detection', 0.0))
                segmentation_times.append(times.get('segmentation', 0.0))
                recognition_times.append(times.get('recognition', 0.0))
            
            if (i + 1) % 20 == 0:
                self.logger.info(f"已完成 {i + 1}/{num_iterations} 次迭代")
        
        # 计算统计信息
        performance_result = {
            'num_iterations': num_iterations,
            'processing_time': {
                'mean': np.mean(processing_times),
                'std': np.std(processing_times),
                'min': np.min(processing_times),
                'max': np.max(processing_times),
                'median': np.median(processing_times),
                'p95': np.percentile(processing_times, 95),
                'p99': np.percentile(processing_times, 99)
            },
            'detection_time': {
                'mean': np.mean(detection_times) if detection_times else 0.0,
                'std': np.std(detection_times) if detection_times else 0.0
            },
            'segmentation_time': {
                'mean': np.mean(segmentation_times) if segmentation_times else 0.0,
                'std': np.std(segmentation_times) if segmentation_times else 0.0
            },
            'recognition_time': {
                'mean': np.mean(recognition_times) if recognition_times else 0.0,
                'std': np.std(recognition_times) if recognition_times else 0.0
            },
            'fps': 1.0 / np.mean(processing_times) if processing_times else 0.0,
            'system_stats': self.recognizer.get_performance_stats()
        }
        
        self.performance_metrics = performance_result
        
        self.logger.info(f"性能测试完成，平均处理时间: {performance_result['processing_time']['mean']:.4f}秒")
        self.logger.info(f"平均FPS: {performance_result['fps']:.2f}")
        
        return performance_result
    
    def run_robustness_test(self, test_images: List[str],
                          noise_levels: List[float] = [0.1, 0.2, 0.3],
                          blur_kernels: List[int] = [3, 5, 7],
                          brightness_factors: List[float] = [0.5, 1.5, 2.0]) -> Dict[str, Any]:
        """
        运行鲁棒性测试
        
        Args:
            test_images (List[str]): 测试图像路径列表
            noise_levels (List[float]): 噪声水平列表
            blur_kernels (List[int]): 模糊核大小列表
            brightness_factors (List[float]): 亮度因子列表
            
        Returns:
            Dict[str, Any]: 鲁棒性测试结果
        """
        self.logger.info("开始鲁棒性测试...")
        
        robustness_results = {
            'original': [],
            'noise': defaultdict(list),
            'blur': defaultdict(list),
            'brightness': defaultdict(list)
        }
        
        for image_path in test_images:
            # 加载原始图像
            original_img = cv2.imread(image_path)
            if original_img is None:
                continue
            
            # 测试原始图像
            original_result = self.recognizer.recognize_from_image(original_img, return_details=True)
            robustness_results['original'].append({
                'image_path': image_path,
                'license_plate': original_result.get('license_plate', ''),
                'confidence': original_result.get('confidence', 0.0)
            })
            
            # 测试噪声影响
            for noise_level in noise_levels:
                noisy_img = self._add_noise(original_img, noise_level)
                noisy_result = self.recognizer.recognize_from_image(noisy_img, return_details=True)
                robustness_results['noise'][noise_level].append({
                    'image_path': image_path,
                    'license_plate': noisy_result.get('license_plate', ''),
                    'confidence': noisy_result.get('confidence', 0.0)
                })
            
            # 测试模糊影响
            for blur_kernel in blur_kernels:
                blurred_img = cv2.GaussianBlur(original_img, (blur_kernel, blur_kernel), 0)
                blurred_result = self.recognizer.recognize_from_image(blurred_img, return_details=True)
                robustness_results['blur'][blur_kernel].append({
                    'image_path': image_path,
                    'license_plate': blurred_result.get('license_plate', ''),
                    'confidence': blurred_result.get('confidence', 0.0)
                })
            
            # 测试亮度影响
            for brightness_factor in brightness_factors:
                bright_img = self._adjust_brightness(original_img, brightness_factor)
                bright_result = self.recognizer.recognize_from_image(bright_img, return_details=True)
                robustness_results['brightness'][brightness_factor].append({
                    'image_path': image_path,
                    'license_plate': bright_result.get('license_plate', ''),
                    'confidence': bright_result.get('confidence', 0.0)
                })
        
        # 分析鲁棒性
        robustness_analysis = self._analyze_robustness(robustness_results)
        
        self.logger.info("鲁棒性测试完成")
        
        return {
            'results': robustness_results,
            'analysis': robustness_analysis
        }
    
    def _load_ground_truth(self, ground_truth_file: str) -> Dict[str, str]:
        """加载真实标签"""
        ground_truth_path = Path(ground_truth_file)
        
        if ground_truth_path.suffix.lower() == '.json':
            with open(ground_truth_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        elif ground_truth_path.suffix.lower() == '.csv':
            df = pd.read_csv(ground_truth_path)
            return dict(zip(df['image_name'], df['license_plate']))
        else:
            raise ValueError(f"不支持的标签文件格式: {ground_truth_path.suffix}")
    
    def _analyze_errors(self, detailed_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析错误类型"""
        error_types = {
            'detection_failed': 0,
            'recognition_failed': 0,
            'partial_correct': 0,
            'completely_wrong': 0
        }
        
        for result in detailed_results:
            if not result['is_correct']:
                if result['error'] and 'detection' in result['error'].lower():
                    error_types['detection_failed'] += 1
                elif result['predicted_label'] == '':
                    error_types['recognition_failed'] += 1
                elif self._is_partial_match(result['true_label'], result['predicted_label']):
                    error_types['partial_correct'] += 1
                else:
                    error_types['completely_wrong'] += 1
        
        return error_types
    
    def _analyze_confidence(self, detailed_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析置信度统计"""
        correct_confidences = [r['confidence'] for r in detailed_results if r['is_correct']]
        incorrect_confidences = [r['confidence'] for r in detailed_results if not r['is_correct']]
        
        return {
            'correct_predictions': {
                'mean': np.mean(correct_confidences) if correct_confidences else 0.0,
                'std': np.std(correct_confidences) if correct_confidences else 0.0,
                'count': len(correct_confidences)
            },
            'incorrect_predictions': {
                'mean': np.mean(incorrect_confidences) if incorrect_confidences else 0.0,
                'std': np.std(incorrect_confidences) if incorrect_confidences else 0.0,
                'count': len(incorrect_confidences)
            }
        }
    
    def _analyze_robustness(self, robustness_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析鲁棒性结果"""
        analysis = {}
        
        # 原始图像成功率
        original_success = len([r for r in robustness_results['original'] if r['license_plate']])
        total_images = len(robustness_results['original'])
        analysis['original_success_rate'] = original_success / total_images if total_images > 0 else 0.0
        
        # 噪声影响分析
        analysis['noise_impact'] = {}
        for noise_level, results in robustness_results['noise'].items():
            success_count = len([r for r in results if r['license_plate']])
            analysis['noise_impact'][noise_level] = success_count / len(results) if results else 0.0
        
        # 模糊影响分析
        analysis['blur_impact'] = {}
        for blur_kernel, results in robustness_results['blur'].items():
            success_count = len([r for r in results if r['license_plate']])
            analysis['blur_impact'][blur_kernel] = success_count / len(results) if results else 0.0
        
        # 亮度影响分析
        analysis['brightness_impact'] = {}
        for brightness_factor, results in robustness_results['brightness'].items():
            success_count = len([r for r in results if r['license_plate']])
            analysis['brightness_impact'][brightness_factor] = success_count / len(results) if results else 0.0
        
        return analysis
    
    def _is_partial_match(self, true_label: str, predicted_label: str) -> bool:
        """判断是否部分匹配"""
        if not true_label or not predicted_label:
            return False
        
        # 计算字符级别的匹配度
        min_len = min(len(true_label), len(predicted_label))
        matches = sum(1 for i in range(min_len) if true_label[i] == predicted_label[i])
        
        return matches / len(true_label) >= 0.5  # 至少50%匹配
    
    def _add_noise(self, image: np.ndarray, noise_level: float) -> np.ndarray:
        """添加噪声"""
        noise = np.random.normal(0, noise_level * 255, image.shape).astype(np.uint8)
        noisy_image = cv2.add(image, noise)
        return noisy_image
    
    def _adjust_brightness(self, image: np.ndarray, factor: float) -> np.ndarray:
        """调整亮度"""
        bright_image = cv2.convertScaleAbs(image, alpha=factor, beta=0)
        return bright_image
    
    def generate_test_report(self, save_path: str) -> None:
        """
        生成测试报告
        
        Args:
            save_path (str): 保存路径
        """
        report = {
            'test_results': self.test_results,
            'performance_metrics': self.performance_metrics,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"测试报告已保存: {save_path}")
    
    def plot_performance_metrics(self, save_path: Optional[str] = None) -> None:
        """
        绘制性能指标图表
        
        Args:
            save_path (Optional[str]): 保存路径
        """
        if not self.performance_metrics:
            self.logger.warning("没有性能指标数据")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 处理时间分布
        processing_time = self.performance_metrics['processing_time']
        axes[0, 0].bar(['Mean', 'Median', 'P95', 'P99'], 
                      [processing_time['mean'], processing_time['median'],
                       processing_time['p95'], processing_time['p99']])
        axes[0, 0].set_title('处理时间分布')
        axes[0, 0].set_ylabel('时间 (秒)')
        
        # 各阶段时间对比
        stage_times = [
            self.performance_metrics['detection_time']['mean'],
            self.performance_metrics['segmentation_time']['mean'],
            self.performance_metrics['recognition_time']['mean']
        ]
        axes[0, 1].pie(stage_times, labels=['检测', '分割', '识别'], autopct='%1.1f%%')
        axes[0, 1].set_title('各阶段时间占比')
        
        # FPS显示
        fps = self.performance_metrics['fps']
        axes[1, 0].bar(['FPS'], [fps])
        axes[1, 0].set_title(f'处理速度: {fps:.2f} FPS')
        axes[1, 0].set_ylabel('帧率')
        
        # 成功率统计
        system_stats = self.performance_metrics['system_stats']
        success_rates = [
            system_stats['detection_success_rate'],
            system_stats['recognition_success_rate']
        ]
        axes[1, 1].bar(['检测成功率', '识别成功率'], success_rates)
        axes[1, 1].set_title('系统成功率')
        axes[1, 1].set_ylabel('成功率')
        axes[1, 1].set_ylim(0, 1)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"性能指标图表已保存: {save_path}")
        
        plt.show()
    
    def plot_accuracy_analysis(self, test_result: Dict[str, Any],
                             save_path: Optional[str] = None) -> None:
        """
        绘制准确率分析图表
        
        Args:
            test_result (Dict[str, Any]): 测试结果
            save_path (Optional[str]): 保存路径
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 总体准确率
        accuracy = test_result['accuracy']
        axes[0, 0].bar(['准确率'], [accuracy])
        axes[0, 0].set_title(f'总体准确率: {accuracy:.4f}')
        axes[0, 0].set_ylabel('准确率')
        axes[0, 0].set_ylim(0, 1)
        
        # 错误类型分析
        error_analysis = test_result['error_analysis']
        error_types = list(error_analysis.keys())
        error_counts = list(error_analysis.values())
        axes[0, 1].pie(error_counts, labels=error_types, autopct='%1.1f%%')
        axes[0, 1].set_title('错误类型分布')
        
        # 置信度分析
        confidence_stats = test_result['confidence_stats']
        correct_conf = confidence_stats['correct_predictions']
        incorrect_conf = confidence_stats['incorrect_predictions']
        
        axes[1, 0].bar(['正确预测', '错误预测'], 
                      [correct_conf['mean'], incorrect_conf['mean']])
        axes[1, 0].set_title('置信度对比')
        axes[1, 0].set_ylabel('平均置信度')
        
        # 置信度分布
        detailed_results = test_result['detailed_results']
        correct_confidences = [r['confidence'] for r in detailed_results if r['is_correct']]
        incorrect_confidences = [r['confidence'] for r in detailed_results if not r['is_correct']]
        
        axes[1, 1].hist([correct_confidences, incorrect_confidences], 
                       bins=20, alpha=0.7, label=['正确', '错误'])
        axes[1, 1].set_title('置信度分布')
        axes[1, 1].set_xlabel('置信度')
        axes[1, 1].set_ylabel('频次')
        axes[1, 1].legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"准确率分析图表已保存: {save_path}")
        
        plt.show()
