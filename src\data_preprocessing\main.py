#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预处理主程序
Data Preprocessing Main Program

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import argparse
from pathlib import Path
from typing import Dict, Any, List

from .data_cleaner import DataCleaner
from .image_processor import ImageProcessor
from .dataset_generator import DatasetGenerator
from .annotation_tool import create_annotation_tool
from ..utils.logger import LoggerMixin


class DataPreprocessingManager(LoggerMixin):
    """数据预处理管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化管理器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.data_cleaner = DataCleaner(config)
        self.image_processor = ImageProcessor(config)
        self.dataset_generator = DatasetGenerator(config)

        self.logger.info("数据预处理管理器初始化完成")
    
    def run_full_preprocessing(self) -> Dict[str, Any]:
        """
        运行完整的数据预处理流程
        
        Returns:
            Dict[str, Any]: 处理结果统计
        """
        self.logger.info("开始完整数据预处理流程")
        
        # 1. 数据清洗
        self.logger.info("步骤1: 数据清洗")
        cleaning_stats = self.data_cleaner.clean_all_data()
        
        # 2. 移除低质量图像
        self.logger.info("步骤2: 移除低质量图像")
        removed_count = self.data_cleaner.remove_low_quality_images(quality_threshold=100.0)

        # 3. 生成数据集
        self.logger.info("步骤3: 生成训练数据集")
        dataset_results = self.dataset_generator.generate_datasets()

        # 4. 生成处理报告
        results = {
            'cleaning_stats': cleaning_stats,
            'removed_low_quality': removed_count,
            'dataset_results': dataset_results,
            'final_image_count': self._count_processed_images()
        }
        
        self.logger.info("数据预处理完成")
        self.logger.info(f"最终图像数量: {results['final_image_count']}")
        
        return results
    
    def clean_data_only(self) -> Dict[str, Any]:
        """
        仅执行数据清洗
        
        Returns:
            Dict[str, Any]: 清洗结果统计
        """
        self.logger.info("执行数据清洗")
        return self.data_cleaner.clean_all_data()
    
    def remove_duplicates(self) -> int:
        """
        移除重复图像
        
        Returns:
            int: 移除的重复图像数量
        """
        self.logger.info("移除重复图像")
        # 这个功能已经集成在数据清洗中
        return 0
    
    def enhance_image_quality(self, input_dir: str, output_dir: str) -> int:
        """
        批量增强图像质量
        
        Args:
            input_dir (str): 输入目录
            output_dir (str): 输出目录
            
        Returns:
            int: 处理的图像数量
        """
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        processed_count = 0
        
        # 获取所有图像文件
        image_files = list(input_path.glob('*.jpg')) + \
                     list(input_path.glob('*.png')) + \
                     list(input_path.glob('*.bmp'))
        
        self.logger.info(f"开始增强 {len(image_files)} 张图像")
        
        for img_file in image_files:
            try:
                import cv2
                image = cv2.imread(str(img_file))
                if image is not None:
                    # 增强图像质量
                    enhanced = self.image_processor.enhance_image_quality(image)
                    
                    # 保存增强后的图像
                    output_file = output_path / img_file.name
                    cv2.imwrite(str(output_file), enhanced)
                    processed_count += 1
                    
            except Exception as e:
                self.logger.error(f"增强图像失败 {img_file}: {str(e)}")
        
        self.logger.info(f"图像质量增强完成，处理了 {processed_count} 张图像")
        return processed_count
    
    def validate_processed_data(self) -> Dict[str, Any]:
        """
        验证处理后的数据
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        self.logger.info("验证处理后的数据")
        
        processed_data_path = Path(self.config['data']['processed_data_path'])
        
        validation_results = {
            'total_images': 0,
            'valid_images': 0,
            'invalid_images': 0,
            'data_sources': {},
            'quality_stats': {
                'high_quality': 0,
                'medium_quality': 0,
                'low_quality': 0
            }
        }
        
        # 检查各个数据源目录
        for source_dir in processed_data_path.iterdir():
            if source_dir.is_dir():
                source_stats = self._validate_source_directory(source_dir)
                validation_results['data_sources'][source_dir.name] = source_stats
                
                # 累加统计
                validation_results['total_images'] += source_stats['total_images']
                validation_results['valid_images'] += source_stats['valid_images']
                validation_results['invalid_images'] += source_stats['invalid_images']
                
                for quality_level in ['high_quality', 'medium_quality', 'low_quality']:
                    validation_results['quality_stats'][quality_level] += \
                        source_stats['quality_stats'][quality_level]
        
        # 输出验证结果
        self.logger.info(f"验证完成:")
        self.logger.info(f"  总图像数: {validation_results['total_images']}")
        self.logger.info(f"  有效图像: {validation_results['valid_images']}")
        self.logger.info(f"  无效图像: {validation_results['invalid_images']}")
        self.logger.info(f"  高质量: {validation_results['quality_stats']['high_quality']}")
        self.logger.info(f"  中等质量: {validation_results['quality_stats']['medium_quality']}")
        self.logger.info(f"  低质量: {validation_results['quality_stats']['low_quality']}")
        
        return validation_results
    
    def _validate_source_directory(self, source_dir: Path) -> Dict[str, Any]:
        """
        验证单个数据源目录
        
        Args:
            source_dir (Path): 数据源目录
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        import cv2
        
        stats = {
            'total_images': 0,
            'valid_images': 0,
            'invalid_images': 0,
            'quality_stats': {
                'high_quality': 0,
                'medium_quality': 0,
                'low_quality': 0
            }
        }
        
        image_files = list(source_dir.glob('*.jpg'))
        stats['total_images'] = len(image_files)
        
        for img_file in image_files:
            try:
                image = cv2.imread(str(img_file))
                if image is not None:
                    # 验证图像
                    is_valid, _ = self.image_processor.validate_image(image)
                    if is_valid:
                        stats['valid_images'] += 1
                        
                        # 评估图像质量
                        quality_level = self._assess_image_quality(image)
                        stats['quality_stats'][quality_level] += 1
                    else:
                        stats['invalid_images'] += 1
                else:
                    stats['invalid_images'] += 1
                    
            except Exception:
                stats['invalid_images'] += 1
        
        return stats
    
    def _assess_image_quality(self, image) -> str:
        """
        评估图像质量
        
        Args:
            image: 输入图像
            
        Returns:
            str: 质量等级 ('high_quality', 'medium_quality', 'low_quality')
        """
        import cv2
        
        # 计算拉普拉斯方差（清晰度指标）
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        
        if laplacian_var > 500:
            return 'high_quality'
        elif laplacian_var > 200:
            return 'medium_quality'
        else:
            return 'low_quality'
    
    def _count_processed_images(self) -> int:
        """
        统计处理后的图像数量
        
        Returns:
            int: 图像数量
        """
        processed_data_path = Path(self.config['data']['processed_data_path'])
        count = 0
        
        for source_dir in processed_data_path.iterdir():
            if source_dir.is_dir():
                count += len(list(source_dir.glob('*.jpg')))
        
        return count

    def generate_datasets_only(self) -> Dict[str, Any]:
        """
        仅生成数据集

        Returns:
            Dict[str, Any]: 数据集生成结果
        """
        self.logger.info("生成训练数据集")
        return self.dataset_generator.generate_datasets()

    def create_custom_dataset(self, data_sources: List[str], output_name: str) -> Dict[str, Any]:
        """
        创建自定义数据集

        Args:
            data_sources (List[str]): 数据源列表
            output_name (str): 输出数据集名称

        Returns:
            Dict[str, Any]: 创建结果
        """
        self.logger.info(f"创建自定义数据集: {output_name}")
        return self.dataset_generator.create_custom_dataset(data_sources, output_name)

    def launch_annotation_tool(self) -> None:
        """启动数据标注工具"""
        self.logger.info("启动数据标注工具")
        annotation_tool = create_annotation_tool(self.config)
        annotation_tool.run()


def main():
    """数据预处理主程序"""
    parser = argparse.ArgumentParser(description='车牌识别数据预处理工具')
    parser.add_argument('--mode', type=str,
                       choices=['full', 'clean', 'enhance', 'validate', 'generate', 'annotate', 'custom'],
                       default='full', help='处理模式')
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--input-dir', type=str,
                       help='输入目录（用于enhance模式）')
    parser.add_argument('--output-dir', type=str,
                       help='输出目录（用于enhance模式）')
    parser.add_argument('--data-sources', type=str, nargs='+',
                       help='数据源列表（用于custom模式）')
    parser.add_argument('--output-name', type=str,
                       help='输出数据集名称（用于custom模式）')
    
    args = parser.parse_args()
    
    # 加载配置
    from ..utils.config_loader import load_config
    config = load_config(args.config)
    
    # 创建数据预处理管理器
    manager = DataPreprocessingManager(config)
    
    if args.mode == 'full':
        results = manager.run_full_preprocessing()
        print(f"预处理完成，最终图像数量: {results['final_image_count']}")
    elif args.mode == 'clean':
        results = manager.clean_data_only()
        print(f"数据清洗完成，处理了 {results['processed_files']} 张图像")
    elif args.mode == 'enhance':
        if not args.input_dir or not args.output_dir:
            print("enhance模式需要指定--input-dir和--output-dir参数")
            return
        count = manager.enhance_image_quality(args.input_dir, args.output_dir)
        print(f"图像增强完成，处理了 {count} 张图像")
    elif args.mode == 'validate':
        results = manager.validate_processed_data()
        print(f"数据验证完成，有效图像: {results['valid_images']}/{results['total_images']}")
    elif args.mode == 'generate':
        results = manager.generate_datasets_only()
        if 'error' not in results:
            total_samples = sum(r['success_count'] for r in results.values())
            print(f"数据集生成完成，总样本数: {total_samples}")
        else:
            print(f"数据集生成失败: {results['error']}")
    elif args.mode == 'annotate':
        manager.launch_annotation_tool()
    elif args.mode == 'custom':
        if not args.data_sources or not args.output_name:
            print("custom模式需要指定--data-sources和--output-name参数")
            return
        results = manager.create_custom_dataset(args.data_sources, args.output_name)
        if 'error' not in results:
            print(f"自定义数据集创建完成: {results['success_count']} 个样本")
        else:
            print(f"自定义数据集创建失败: {results['error']}")


if __name__ == "__main__":
    main()
