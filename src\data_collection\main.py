#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据采集主程序
Data Collection Main Program

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import argparse
from pathlib import Path
from typing import Dict, Any

from .crawler import run_data_collection
from .dataset_downloader import download_public_datasets
from .synthetic_generator import SyntheticPlateGenerator
from ..utils.logger import LoggerMixin


class DataCollectionManager(LoggerMixin):
    """数据采集管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化管理器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.logger.info("数据采集管理器初始化完成")
    
    def collect_web_data(self, max_images: int = 5000) -> None:
        """
        从网络采集数据
        
        Args:
            max_images (int): 最大图像数量
        """
        self.logger.info("开始网络数据采集")
        run_data_collection(self.config)
        self.logger.info("网络数据采集完成")
    
    def download_datasets(self, dataset_keys: list = None) -> None:
        """
        下载公开数据集
        
        Args:
            dataset_keys (list): 数据集键名列表
        """
        self.logger.info("开始下载公开数据集")
        if dataset_keys is None:
            dataset_keys = ['synthetic_plates']  # 默认生成合成数据
        download_public_datasets(self.config, dataset_keys)
        self.logger.info("公开数据集下载完成")
    
    def generate_synthetic_data(self, num_samples: int = 1000) -> None:
        """
        生成合成数据
        
        Args:
            num_samples (int): 样本数量
        """
        self.logger.info(f"开始生成 {num_samples} 个合成样本")
        
        generator = SyntheticPlateGenerator(self.config)
        
        # 输出目录
        raw_data_path = Path(self.config['data']['raw_data_path'])
        synthetic_dir = raw_data_path / 'synthetic_plates'
        
        success_count = generator.generate_dataset(synthetic_dir, num_samples)
        
        self.logger.info(f"合成数据生成完成，成功生成 {success_count} 个样本")
    
    def collect_all_data(self, web_images: int = 2000, synthetic_samples: int = 3000) -> None:
        """
        采集所有类型的数据
        
        Args:
            web_images (int): 网络图像数量
            synthetic_samples (int): 合成样本数量
        """
        self.logger.info("开始全面数据采集")
        
        # 1. 生成合成数据（最可靠的数据源）
        self.generate_synthetic_data(synthetic_samples)
        
        # 2. 下载公开数据集
        self.download_datasets(['synthetic_plates'])
        
        # 3. 网络数据采集（可选，需要谨慎使用）
        # self.collect_web_data(web_images)
        
        self.logger.info("全面数据采集完成")
    
    def validate_collected_data(self) -> Dict[str, Any]:
        """
        验证采集的数据
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        self.logger.info("开始验证采集的数据")
        
        raw_data_path = Path(self.config['data']['raw_data_path'])
        
        validation_results = {
            'total_images': 0,
            'valid_images': 0,
            'invalid_images': 0,
            'data_sources': {},
            'errors': []
        }
        
        # 检查各个数据源目录
        for data_source_dir in raw_data_path.iterdir():
            if data_source_dir.is_dir():
                source_name = data_source_dir.name
                image_files = list(data_source_dir.glob('**/*.jpg')) + \
                             list(data_source_dir.glob('**/*.png')) + \
                             list(data_source_dir.glob('**/*.bmp'))
                
                source_stats = {
                    'total_files': len(image_files),
                    'valid_files': 0,
                    'invalid_files': 0
                }
                
                # 验证每个图像文件
                for img_file in image_files:
                    try:
                        import cv2
                        img = cv2.imread(str(img_file))
                        if img is not None and img.shape[0] > 0 and img.shape[1] > 0:
                            source_stats['valid_files'] += 1
                            validation_results['valid_images'] += 1
                        else:
                            source_stats['invalid_files'] += 1
                            validation_results['invalid_images'] += 1
                    except Exception as e:
                        source_stats['invalid_files'] += 1
                        validation_results['invalid_images'] += 1
                        validation_results['errors'].append(f"文件 {img_file}: {str(e)}")
                
                validation_results['data_sources'][source_name] = source_stats
                validation_results['total_images'] += source_stats['total_files']
        
        # 输出验证结果
        self.logger.info(f"数据验证完成:")
        self.logger.info(f"  总图像数: {validation_results['total_images']}")
        self.logger.info(f"  有效图像: {validation_results['valid_images']}")
        self.logger.info(f"  无效图像: {validation_results['invalid_images']}")
        
        for source, stats in validation_results['data_sources'].items():
            self.logger.info(f"  {source}: {stats['valid_files']}/{stats['total_files']} 有效")
        
        if validation_results['errors']:
            self.logger.warning(f"发现 {len(validation_results['errors'])} 个错误")
        
        return validation_results


def main():
    """数据采集主程序"""
    parser = argparse.ArgumentParser(description='车牌识别数据采集工具')
    parser.add_argument('--mode', type=str, 
                       choices=['web', 'datasets', 'synthetic', 'all', 'validate'],
                       default='synthetic', help='采集模式')
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--web-images', type=int, default=2000,
                       help='网络图像数量')
    parser.add_argument('--synthetic-samples', type=int, default=3000,
                       help='合成样本数量')
    parser.add_argument('--datasets', nargs='+', 
                       help='要下载的数据集列表')
    
    args = parser.parse_args()
    
    # 加载配置
    from ..utils.config_loader import load_config
    config = load_config(args.config)
    
    # 创建数据采集管理器
    manager = DataCollectionManager(config)
    
    if args.mode == 'web':
        manager.collect_web_data(args.web_images)
    elif args.mode == 'datasets':
        manager.download_datasets(args.datasets)
    elif args.mode == 'synthetic':
        manager.generate_synthetic_data(args.synthetic_samples)
    elif args.mode == 'all':
        manager.collect_all_data(args.web_images, args.synthetic_samples)
    elif args.mode == 'validate':
        manager.validate_collected_data()
    
    print("数据采集完成！")


if __name__ == "__main__":
    main()
