#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字符识别模型
Character Recognition Model

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, List, Tuple
import torchvision.models as models

from .base_model import BaseModel


class CharacterRecognizer(BaseModel):
    """车牌字符识别模型"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化字符识别模型
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        super(CharacterRecognizer, self).__init__(config)
        
        # 模型参数
        self.input_size = (64, 64)  # 单个字符图像尺寸
        self.num_classes = 66  # 31个省份 + 24个字母 + 10个数字 + 1个未知
        
        # 构建模型
        self.build_model()
        
        # 移动到设备
        self.to_device()
        
        self.logger.info(f"字符识别模型初始化完成，参数数量: {self.count_parameters():,}")
    
    def build_model(self) -> None:
        """构建模型架构"""
        # 使用ResNet18作为骨干网络
        backbone = models.resnet18(pretrained=True)
        
        # 修改第一层以适应单字符输入
        backbone.conv1 = nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3, bias=False)
        
        # 移除最后的全连接层
        self.backbone = nn.Sequential(*list(backbone.children())[:-1])
        
        # 添加自定义分类头
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, self.num_classes)
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x (torch.Tensor): 输入字符图像 [B, C, H, W]
            
        Returns:
            torch.Tensor: 分类logits [B, num_classes]
        """
        # 通过骨干网络
        features = self.backbone(x)
        features = features.view(features.size(0), -1)  # 展平
        
        # 分类
        logits = self.classifier(features)
        
        return logits
    
    def predict(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        预测字符
        
        Args:
            x (torch.Tensor): 输入字符图像
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 预测类别和置信度
        """
        self.eval()
        with torch.no_grad():
            logits = self.forward(x)
            probabilities = F.softmax(logits, dim=1)
            predicted_classes = torch.argmax(probabilities, dim=1)
            confidences = torch.max(probabilities, dim=1)[0]
            
            return predicted_classes, confidences
    
    def predict_sequence(self, char_images: List[torch.Tensor]) -> Tuple[List[int], List[float]]:
        """
        预测字符序列
        
        Args:
            char_images (List[torch.Tensor]): 字符图像列表
            
        Returns:
            Tuple[List[int], List[float]]: 预测类别列表和置信度列表
        """
        if not char_images:
            return [], []
        
        # 批量处理
        batch_images = torch.stack(char_images)
        predicted_classes, confidences = self.predict(batch_images)
        
        return predicted_classes.tolist(), confidences.tolist()


class CharacterRecognizerCRNN(BaseModel):
    """基于CRNN的字符识别模型（用于整个车牌识别）"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化CRNN字符识别模型
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        super(CharacterRecognizerCRNN, self).__init__(config)
        
        # 模型参数
        self.input_size = (32, 128)  # 整个车牌图像尺寸
        self.num_classes = 66  # 字符类别数
        self.max_sequence_length = 8  # 最大字符序列长度
        self.hidden_size = 256
        
        # 构建模型
        self.build_model()
        
        # 移动到设备
        self.to_device()
        
        self.logger.info(f"CRNN字符识别模型初始化完成，参数数量: {self.count_parameters():,}")
    
    def build_model(self) -> None:
        """构建CRNN模型架构"""
        # CNN特征提取器
        self.cnn = nn.Sequential(
            # 第一个卷积块
            nn.Conv2d(3, 64, 3, 1, 1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),  # 32x128 -> 16x64
            
            # 第二个卷积块
            nn.Conv2d(64, 128, 3, 1, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),  # 16x64 -> 8x32
            
            # 第三个卷积块
            nn.Conv2d(128, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool2d((2, 1), (2, 1)),  # 8x32 -> 4x32
            
            # 第四个卷积块
            nn.Conv2d(256, 512, 3, 1, 1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, 3, 1, 1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.MaxPool2d((2, 1), (2, 1)),  # 4x32 -> 2x32
            
            # 第五个卷积块
            nn.Conv2d(512, 512, 2, 1, 0),  # 2x32 -> 1x31
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
        )
        
        # RNN序列建模
        self.rnn = nn.LSTM(
            input_size=512,
            hidden_size=self.hidden_size,
            num_layers=2,
            bidirectional=True,
            batch_first=True
        )
        
        # 输出层
        self.output_layer = nn.Linear(self.hidden_size * 2, self.num_classes)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x (torch.Tensor): 输入车牌图像 [B, C, H, W]
            
        Returns:
            torch.Tensor: 序列预测结果 [B, T, num_classes]
        """
        # CNN特征提取
        conv_features = self.cnn(x)  # [B, 512, 1, W']
        
        # 重塑为序列格式
        batch_size, channels, height, width = conv_features.size()
        conv_features = conv_features.squeeze(2)  # [B, 512, W']
        conv_features = conv_features.permute(0, 2, 1)  # [B, W', 512]
        
        # RNN序列建模
        rnn_output, _ = self.rnn(conv_features)  # [B, W', hidden_size*2]
        
        # 输出层
        output = self.output_layer(rnn_output)  # [B, W', num_classes]
        
        return output
    
    def predict(self, x: torch.Tensor) -> List[Tuple[List[int], List[float]]]:
        """
        预测字符序列
        
        Args:
            x (torch.Tensor): 输入车牌图像
            
        Returns:
            List[Tuple[List[int], List[float]]]: 每个样本的预测序列和置信度
        """
        self.eval()
        with torch.no_grad():
            # 前向传播
            output = self.forward(x)  # [B, T, num_classes]
            
            # 应用softmax
            probabilities = F.softmax(output, dim=2)
            
            results = []
            for batch_idx in range(output.size(0)):
                batch_probs = probabilities[batch_idx]  # [T, num_classes]
                
                # 贪婪解码
                predicted_sequence = []
                confidence_sequence = []
                
                for time_step in range(batch_probs.size(0)):
                    step_probs = batch_probs[time_step]
                    predicted_class = torch.argmax(step_probs).item()
                    confidence = step_probs[predicted_class].item()
                    
                    # 简单的CTC解码（去除重复和空白）
                    if predicted_class != 0:  # 假设0是空白字符
                        if not predicted_sequence or predicted_class != predicted_sequence[-1]:
                            predicted_sequence.append(predicted_class)
                            confidence_sequence.append(confidence)
                
                results.append((predicted_sequence, confidence_sequence))
            
            return results
    
    def decode_sequence(self, sequence: List[int], 
                       char_mapping: Dict[int, str]) -> str:
        """
        将预测序列解码为字符串
        
        Args:
            sequence (List[int]): 预测的类别序列
            char_mapping (Dict[int, str]): 类别到字符的映射
            
        Returns:
            str: 解码后的字符串
        """
        decoded_chars = []
        for class_id in sequence:
            if class_id in char_mapping:
                decoded_chars.append(char_mapping[class_id])
            else:
                decoded_chars.append('?')  # 未知字符
        
        return ''.join(decoded_chars)


class CharacterRecognizerTransformer(BaseModel):
    """基于Transformer的字符识别模型"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Transformer字符识别模型
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        super(CharacterRecognizerTransformer, self).__init__(config)
        
        # 模型参数
        self.input_size = (32, 128)
        self.num_classes = 66
        self.d_model = 512
        self.nhead = 8
        self.num_layers = 6
        self.max_sequence_length = 8
        
        # 构建模型
        self.build_model()
        
        # 移动到设备
        self.to_device()
        
        self.logger.info(f"Transformer字符识别模型初始化完成，参数数量: {self.count_parameters():,}")
    
    def build_model(self) -> None:
        """构建Transformer模型架构"""
        # CNN特征提取器（与CRNN相同）
        self.cnn = nn.Sequential(
            nn.Conv2d(3, 64, 3, 1, 1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),
            
            nn.Conv2d(64, 128, 3, 1, 1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),
            
            nn.Conv2d(128, 256, 3, 1, 1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.MaxPool2d((2, 1), (2, 1)),
            
            nn.Conv2d(256, 512, 3, 1, 1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.MaxPool2d((2, 1), (2, 1)),
            
            nn.Conv2d(512, self.d_model, 2, 1, 0),
            nn.BatchNorm2d(self.d_model),
            nn.ReLU(inplace=True),
        )
        
        # 位置编码
        self.pos_encoding = self._create_positional_encoding()
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=self.d_model,
            nhead=self.nhead,
            dim_feedforward=2048,
            dropout=0.1,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=self.num_layers)
        
        # 输出层
        self.output_layer = nn.Linear(self.d_model, self.num_classes)
    
    def _create_positional_encoding(self) -> nn.Parameter:
        """创建位置编码"""
        max_len = 100  # 最大序列长度
        pe = torch.zeros(max_len, self.d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, self.d_model, 2).float() * 
                           (-torch.log(torch.tensor(10000.0)) / self.d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        return nn.Parameter(pe.unsqueeze(0), requires_grad=False)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x (torch.Tensor): 输入车牌图像 [B, C, H, W]
            
        Returns:
            torch.Tensor: 序列预测结果 [B, T, num_classes]
        """
        # CNN特征提取
        conv_features = self.cnn(x)  # [B, d_model, 1, W']
        
        # 重塑为序列格式
        batch_size, channels, height, width = conv_features.size()
        conv_features = conv_features.squeeze(2)  # [B, d_model, W']
        conv_features = conv_features.permute(0, 2, 1)  # [B, W', d_model]
        
        # 添加位置编码
        seq_len = conv_features.size(1)
        conv_features = conv_features + self.pos_encoding[:, :seq_len, :]
        
        # Transformer编码
        transformer_output = self.transformer(conv_features)  # [B, W', d_model]
        
        # 输出层
        output = self.output_layer(transformer_output)  # [B, W', num_classes]
        
        return output
