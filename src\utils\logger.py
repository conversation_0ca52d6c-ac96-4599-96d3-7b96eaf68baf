#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志系统
Logging System

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import logging
import sys
from pathlib import Path
from typing import Dict, Any

try:
    from loguru import logger
    HAS_LOGURU = True
except ImportError:
    HAS_LOGURU = False
    logger = None


def setup_logger(config: Dict[str, Any]) -> logging.Logger:
    """
    设置日志系统

    Args:
        config (Dict[str, Any]): 配置字典

    Returns:
        logging.Logger: 配置好的日志器
    """
    # 获取日志配置
    log_config = config.get('logging', {})
    log_level = log_config.get('level', 'INFO')
    log_file = log_config.get('log_file', 'logs/app.log')

    # 创建日志目录
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)

    if HAS_LOGURU:
        # 使用loguru
        logger.remove()

        # 添加控制台输出
        logger.add(
            sys.stdout,
            level=log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            colorize=True
        )

        # 添加文件输出
        logger.add(
            log_file,
            level=log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="10 MB",
            retention="30 days",
            compression="zip",
            encoding="utf-8"
        )

        return logger
    else:
        # 使用标准logging
        std_logger = logging.getLogger('license_plate_recognition')
        std_logger.setLevel(getattr(logging, log_level.upper()))

        # 清除现有处理器
        std_logger.handlers.clear()

        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s:%(funcName)s:%(lineno)d | %(message)s'
        )

        # 添加控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, log_level.upper()))
        console_handler.setFormatter(formatter)
        std_logger.addHandler(console_handler)

        # 添加文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(getattr(logging, log_level.upper()))
        file_handler.setFormatter(formatter)
        std_logger.addHandler(file_handler)

        return std_logger


def get_logger(name: str = None):
    """
    获取日志器实例

    Args:
        name (str): 日志器名称

    Returns:
        Logger: 日志器实例
    """
    if HAS_LOGURU:
        if name:
            return logger.bind(name=name)
        return logger
    else:
        return logging.getLogger(name or 'license_plate_recognition')


class LoggerMixin:
    """日志混入类，为其他类提供日志功能"""

    @property
    def logger(self):
        """获取当前类的日志器"""
        return get_logger(self.__class__.__name__)


# 创建一些常用的日志函数
def log_info(message: str, **kwargs):
    """记录信息日志"""
    if HAS_LOGURU:
        logger.info(message, **kwargs)
    else:
        logging.getLogger('license_plate_recognition').info(message)


def log_warning(message: str, **kwargs):
    """记录警告日志"""
    if HAS_LOGURU:
        logger.warning(message, **kwargs)
    else:
        logging.getLogger('license_plate_recognition').warning(message)


def log_error(message: str, **kwargs):
    """记录错误日志"""
    if HAS_LOGURU:
        logger.error(message, **kwargs)
    else:
        logging.getLogger('license_plate_recognition').error(message)


def log_debug(message: str, **kwargs):
    """记录调试日志"""
    if HAS_LOGURU:
        logger.debug(message, **kwargs)
    else:
        logging.getLogger('license_plate_recognition').debug(message)


def log_exception(message: str, **kwargs):
    """记录异常日志"""
    if HAS_LOGURU:
        logger.exception(message, **kwargs)
    else:
        logging.getLogger('license_plate_recognition').exception(message)
