#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志系统
Logging System

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import logging
import sys
from pathlib import Path
from typing import Dict, Any
from loguru import logger


def setup_logger(config: Dict[str, Any]) -> logging.Logger:
    """
    设置日志系统
    
    Args:
        config (Dict[str, Any]): 配置字典
        
    Returns:
        logging.Logger: 配置好的日志器
    """
    # 获取日志配置
    log_config = config.get('logging', {})
    log_level = log_config.get('level', 'INFO')
    log_file = log_config.get('log_file', 'logs/app.log')
    
    # 创建日志目录
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 移除默认的loguru处理器
    logger.remove()
    
    # 添加控制台输出
    logger.add(
        sys.stdout,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True
    )
    
    # 添加文件输出
    logger.add(
        log_file,
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation="10 MB",
        retention="30 days",
        compression="zip",
        encoding="utf-8"
    )
    
    return logger


def get_logger(name: str = None):
    """
    获取日志器实例
    
    Args:
        name (str): 日志器名称
        
    Returns:
        loguru.Logger: 日志器实例
    """
    if name:
        return logger.bind(name=name)
    return logger


class LoggerMixin:
    """日志混入类，为其他类提供日志功能"""
    
    @property
    def logger(self):
        """获取当前类的日志器"""
        return get_logger(self.__class__.__name__)


# 创建一些常用的日志函数
def log_info(message: str, **kwargs):
    """记录信息日志"""
    logger.info(message, **kwargs)


def log_warning(message: str, **kwargs):
    """记录警告日志"""
    logger.warning(message, **kwargs)


def log_error(message: str, **kwargs):
    """记录错误日志"""
    logger.error(message, **kwargs)


def log_debug(message: str, **kwargs):
    """记录调试日志"""
    logger.debug(message, **kwargs)


def log_exception(message: str, **kwargs):
    """记录异常日志"""
    logger.exception(message, **kwargs)
