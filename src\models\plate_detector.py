#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车牌检测模型
License Plate Detection Model

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, Tuple, List
import torchvision.models as models
from torchvision.ops import nms

from .base_model import BaseModel


class PlateDetector(BaseModel):
    """基于CNN的车牌检测模型"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化车牌检测模型
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        super(PlateDetector, self).__init__(config)
        
        # 模型参数
        self.input_size = (224, 224)
        self.num_classes = 2  # 背景和车牌
        self.num_anchors = 9  # 每个位置的锚框数量
        
        # 构建模型
        self.build_model()
        
        # 移动到设备
        self.to_device()
        
        self.logger.info(f"车牌检测模型初始化完成，参数数量: {self.count_parameters():,}")
    
    def build_model(self) -> None:
        """构建模型架构"""
        # 使用ResNet作为骨干网络
        backbone = models.resnet50(pretrained=True)
        
        # 移除最后的全连接层
        self.backbone = nn.Sequential(*list(backbone.children())[:-2])
        
        # 特征金字塔网络 (FPN)
        self.fpn = self._build_fpn()
        
        # 检测头
        self.detection_head = self._build_detection_head()
        
        # 分类头
        self.classification_head = self._build_classification_head()
        
        # 回归头
        self.regression_head = self._build_regression_head()
    
    def _build_fpn(self) -> nn.Module:
        """构建特征金字塔网络"""
        return nn.ModuleDict({
            'lateral_conv1': nn.Conv2d(2048, 256, 1),
            'lateral_conv2': nn.Conv2d(1024, 256, 1),
            'lateral_conv3': nn.Conv2d(512, 256, 1),
            'lateral_conv4': nn.Conv2d(256, 256, 1),
            
            'output_conv1': nn.Conv2d(256, 256, 3, padding=1),
            'output_conv2': nn.Conv2d(256, 256, 3, padding=1),
            'output_conv3': nn.Conv2d(256, 256, 3, padding=1),
            'output_conv4': nn.Conv2d(256, 256, 3, padding=1),
        })
    
    def _build_detection_head(self) -> nn.Module:
        """构建检测头"""
        return nn.Sequential(
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1),
            nn.ReLU(inplace=True),
        )
    
    def _build_classification_head(self) -> nn.Module:
        """构建分类头"""
        return nn.Conv2d(256, self.num_anchors * self.num_classes, 3, padding=1)
    
    def _build_regression_head(self) -> nn.Module:
        """构建回归头"""
        return nn.Conv2d(256, self.num_anchors * 4, 3, padding=1)  # 4个坐标值
    
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            x (torch.Tensor): 输入图像 [B, C, H, W]
            
        Returns:
            Dict[str, torch.Tensor]: 检测结果
        """
        # 提取多尺度特征
        features = self._extract_features(x)
        
        # FPN处理
        fpn_features = self._apply_fpn(features)
        
        # 检测头处理
        detections = {}
        for level, feature in fpn_features.items():
            # 通过检测头
            detection_feature = self.detection_head(feature)
            
            # 分类和回归
            classification = self.classification_head(detection_feature)
            regression = self.regression_head(detection_feature)
            
            detections[level] = {
                'classification': classification,
                'regression': regression
            }
        
        return detections
    
    def _extract_features(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        提取多尺度特征
        
        Args:
            x (torch.Tensor): 输入图像
            
        Returns:
            Dict[str, torch.Tensor]: 多尺度特征
        """
        features = {}
        
        # 通过ResNet骨干网络
        x = self.backbone[0](x)  # conv1
        x = self.backbone[1](x)  # bn1
        x = self.backbone[2](x)  # relu
        x = self.backbone[3](x)  # maxpool
        
        x = self.backbone[4](x)  # layer1
        features['C2'] = x
        
        x = self.backbone[5](x)  # layer2
        features['C3'] = x
        
        x = self.backbone[6](x)  # layer3
        features['C4'] = x
        
        x = self.backbone[7](x)  # layer4
        features['C5'] = x
        
        return features
    
    def _apply_fpn(self, features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        应用特征金字塔网络
        
        Args:
            features (Dict[str, torch.Tensor]): 输入特征
            
        Returns:
            Dict[str, torch.Tensor]: FPN特征
        """
        # 侧向连接
        P5 = self.fpn['lateral_conv1'](features['C5'])
        P4 = self.fpn['lateral_conv2'](features['C4'])
        P3 = self.fpn['lateral_conv3'](features['C3'])
        P2 = self.fpn['lateral_conv4'](features['C2'])
        
        # 自顶向下路径
        P4 = P4 + F.interpolate(P5, size=P4.shape[-2:], mode='nearest')
        P3 = P3 + F.interpolate(P4, size=P3.shape[-2:], mode='nearest')
        P2 = P2 + F.interpolate(P3, size=P2.shape[-2:], mode='nearest')
        
        # 输出卷积
        P5 = self.fpn['output_conv1'](P5)
        P4 = self.fpn['output_conv2'](P4)
        P3 = self.fpn['output_conv3'](P3)
        P2 = self.fpn['output_conv4'](P2)
        
        return {
            'P2': P2,
            'P3': P3,
            'P4': P4,
            'P5': P5
        }
    
    def predict(self, x: torch.Tensor, confidence_threshold: float = 0.5,
                nms_threshold: float = 0.4) -> List[Dict[str, torch.Tensor]]:
        """
        预测车牌位置
        
        Args:
            x (torch.Tensor): 输入图像
            confidence_threshold (float): 置信度阈值
            nms_threshold (float): NMS阈值
            
        Returns:
            List[Dict[str, torch.Tensor]]: 检测结果列表
        """
        self.eval()
        with torch.no_grad():
            # 前向传播
            detections = self.forward(x)
            
            # 后处理
            results = []
            batch_size = x.size(0)
            
            for batch_idx in range(batch_size):
                batch_boxes = []
                batch_scores = []
                batch_labels = []
                
                # 处理每个特征层的检测结果
                for level, detection in detections.items():
                    classification = detection['classification'][batch_idx]
                    regression = detection['regression'][batch_idx]
                    
                    # 生成锚框
                    anchors = self._generate_anchors(classification.shape[-2:])
                    
                    # 解码边界框
                    boxes = self._decode_boxes(regression, anchors)
                    
                    # 获取分数和标签
                    scores = torch.sigmoid(classification)
                    scores, labels = torch.max(scores, dim=0)
                    
                    # 过滤低置信度检测
                    valid_mask = scores > confidence_threshold
                    boxes = boxes[valid_mask]
                    scores = scores[valid_mask]
                    labels = labels[valid_mask]
                    
                    batch_boxes.append(boxes)
                    batch_scores.append(scores)
                    batch_labels.append(labels)
                
                # 合并所有层的结果
                if batch_boxes:
                    all_boxes = torch.cat(batch_boxes, dim=0)
                    all_scores = torch.cat(batch_scores, dim=0)
                    all_labels = torch.cat(batch_labels, dim=0)
                    
                    # 应用NMS
                    keep_indices = nms(all_boxes, all_scores, nms_threshold)
                    
                    results.append({
                        'boxes': all_boxes[keep_indices],
                        'scores': all_scores[keep_indices],
                        'labels': all_labels[keep_indices]
                    })
                else:
                    results.append({
                        'boxes': torch.empty((0, 4)),
                        'scores': torch.empty((0,)),
                        'labels': torch.empty((0,), dtype=torch.long)
                    })
            
            return results
    
    def _generate_anchors(self, feature_size: Tuple[int, int]) -> torch.Tensor:
        """
        生成锚框
        
        Args:
            feature_size (Tuple[int, int]): 特征图尺寸
            
        Returns:
            torch.Tensor: 锚框坐标
        """
        # 简化的锚框生成，实际应用中需要更复杂的实现
        h, w = feature_size
        anchors = []
        
        # 不同尺度和宽高比的锚框
        scales = [32, 64, 128]
        ratios = [0.5, 1.0, 2.0]
        
        for i in range(h):
            for j in range(w):
                cx = (j + 0.5) * (224 / w)  # 映射到原图坐标
                cy = (i + 0.5) * (224 / h)
                
                for scale in scales:
                    for ratio in ratios:
                        w_anchor = scale * ratio
                        h_anchor = scale / ratio
                        
                        x1 = cx - w_anchor / 2
                        y1 = cy - h_anchor / 2
                        x2 = cx + w_anchor / 2
                        y2 = cy + h_anchor / 2
                        
                        anchors.append([x1, y1, x2, y2])
        
        return torch.tensor(anchors, device=self.device)
    
    def _decode_boxes(self, regression: torch.Tensor, 
                     anchors: torch.Tensor) -> torch.Tensor:
        """
        解码边界框
        
        Args:
            regression (torch.Tensor): 回归预测
            anchors (torch.Tensor): 锚框
            
        Returns:
            torch.Tensor: 解码后的边界框
        """
        # 简化的边界框解码
        # 实际应用中需要更精确的解码方式
        regression = regression.permute(1, 2, 0).contiguous()
        regression = regression.view(-1, 4)
        
        # 应用回归偏移
        decoded_boxes = anchors + regression
        
        return decoded_boxes


class PlateDetectorYOLO(BaseModel):
    """基于YOLO的车牌检测模型"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化YOLO车牌检测模型

        Args:
            config (Dict[str, Any]): 配置字典
        """
        super(PlateDetectorYOLO, self).__init__(config)

        # 模型参数
        self.input_size = (416, 416)
        self.num_classes = 1  # 只检测车牌
        self.num_anchors = 3  # 每个尺度的锚框数量

        # 构建模型
        self.build_model()

        # 移动到设备
        self.to_device()

        self.logger.info(f"YOLO车牌检测模型初始化完成，参数数量: {self.count_parameters():,}")

    def build_model(self) -> None:
        """构建YOLO模型架构"""
        # Darknet骨干网络
        self.backbone = self._build_darknet()

        # 检测头
        self.detection_layers = nn.ModuleList([
            self._build_detection_layer(1024, 512),  # 大目标检测
            self._build_detection_layer(768, 256),   # 中目标检测
            self._build_detection_layer(384, 128),   # 小目标检测
        ])

        # 上采样层
        self.upsample_layers = nn.ModuleList([
            nn.ConvTranspose2d(512, 256, 2, stride=2),
            nn.ConvTranspose2d(256, 128, 2, stride=2),
        ])

    def _build_darknet(self) -> nn.Module:
        """构建Darknet骨干网络"""
        layers = []

        # 输入层
        layers.append(self._conv_block(3, 32, 3, 1, 1))
        layers.append(self._conv_block(32, 64, 3, 2, 1))

        # Darknet blocks
        layers.append(self._darknet_block(64, 32, 1))
        layers.append(self._conv_block(64, 128, 3, 2, 1))
        layers.append(self._darknet_block(128, 64, 2))
        layers.append(self._conv_block(128, 256, 3, 2, 1))
        layers.append(self._darknet_block(256, 128, 8))
        layers.append(self._conv_block(256, 512, 3, 2, 1))
        layers.append(self._darknet_block(512, 256, 8))
        layers.append(self._conv_block(512, 1024, 3, 2, 1))
        layers.append(self._darknet_block(1024, 512, 4))

        return nn.Sequential(*layers)

    def _conv_block(self, in_channels: int, out_channels: int,
                   kernel_size: int, stride: int, padding: int) -> nn.Module:
        """卷积块"""
        return nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.LeakyReLU(0.1, inplace=True)
        )

    def _darknet_block(self, in_channels: int, hidden_channels: int, num_blocks: int) -> nn.Module:
        """Darknet残差块"""
        layers = []
        for _ in range(num_blocks):
            layers.append(nn.Sequential(
                self._conv_block(in_channels, hidden_channels, 1, 1, 0),
                self._conv_block(hidden_channels, in_channels, 3, 1, 1)
            ))
        return nn.Sequential(*layers)

    def _build_detection_layer(self, in_channels: int, hidden_channels: int) -> nn.Module:
        """构建检测层"""
        return nn.Sequential(
            self._conv_block(in_channels, hidden_channels, 1, 1, 0),
            self._conv_block(hidden_channels, hidden_channels * 2, 3, 1, 1),
            self._conv_block(hidden_channels * 2, hidden_channels, 1, 1, 0),
            self._conv_block(hidden_channels, hidden_channels * 2, 3, 1, 1),
            self._conv_block(hidden_channels * 2, hidden_channels, 1, 1, 0),
            nn.Conv2d(hidden_channels, self.num_anchors * (5 + self.num_classes), 1, 1, 0)
        )

    def forward(self, x: torch.Tensor) -> List[torch.Tensor]:
        """
        前向传播

        Args:
            x (torch.Tensor): 输入图像 [B, C, H, W]

        Returns:
            List[torch.Tensor]: 多尺度检测结果
        """
        # 通过骨干网络提取特征
        features = []
        for i, layer in enumerate(self.backbone):
            x = layer(x)
            # 保存用于多尺度检测的特征
            if i in [10, 13, 16]:  # 对应不同尺度的特征层
                features.append(x)

        # 多尺度检测
        detections = []

        # 大目标检测 (最深层特征)
        detection = self.detection_layers[0](features[-1])
        detections.append(detection)

        # 中目标检测
        upsampled = self.upsample_layers[0](features[-1])
        concat_feature = torch.cat([upsampled, features[-2]], dim=1)
        detection = self.detection_layers[1](concat_feature)
        detections.append(detection)

        # 小目标检测
        upsampled = self.upsample_layers[1](concat_feature)
        concat_feature = torch.cat([upsampled, features[-3]], dim=1)
        detection = self.detection_layers[2](concat_feature)
        detections.append(detection)

        return detections

    def predict(self, x: torch.Tensor, confidence_threshold: float = 0.5,
                nms_threshold: float = 0.4) -> List[Dict[str, torch.Tensor]]:
        """
        预测车牌位置

        Args:
            x (torch.Tensor): 输入图像
            confidence_threshold (float): 置信度阈值
            nms_threshold (float): NMS阈值

        Returns:
            List[Dict[str, torch.Tensor]]: 检测结果列表
        """
        self.eval()
        with torch.no_grad():
            # 前向传播
            detections = self.forward(x)

            # 后处理
            results = []
            batch_size = x.size(0)

            for batch_idx in range(batch_size):
                batch_boxes = []
                batch_scores = []

                # 处理每个尺度的检测结果
                for scale_idx, detection in enumerate(detections):
                    batch_detection = detection[batch_idx]

                    # 解析检测结果
                    boxes, scores = self._parse_yolo_output(
                        batch_detection, scale_idx, confidence_threshold
                    )

                    if len(boxes) > 0:
                        batch_boxes.append(boxes)
                        batch_scores.append(scores)

                # 合并所有尺度的结果
                if batch_boxes:
                    all_boxes = torch.cat(batch_boxes, dim=0)
                    all_scores = torch.cat(batch_scores, dim=0)

                    # 应用NMS
                    keep_indices = nms(all_boxes, all_scores, nms_threshold)

                    results.append({
                        'boxes': all_boxes[keep_indices],
                        'scores': all_scores[keep_indices],
                        'labels': torch.ones(len(keep_indices), dtype=torch.long, device=self.device)
                    })
                else:
                    results.append({
                        'boxes': torch.empty((0, 4), device=self.device),
                        'scores': torch.empty((0,), device=self.device),
                        'labels': torch.empty((0,), dtype=torch.long, device=self.device)
                    })

            return results

    def _parse_yolo_output(self, detection: torch.Tensor, scale_idx: int,
                          confidence_threshold: float) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        解析YOLO输出

        Args:
            detection (torch.Tensor): 检测输出
            scale_idx (int): 尺度索引
            confidence_threshold (float): 置信度阈值

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 边界框和分数
        """
        # 重塑输出张量
        grid_h, grid_w = detection.shape[-2:]
        detection = detection.view(self.num_anchors, 5 + self.num_classes, grid_h, grid_w)
        detection = detection.permute(0, 2, 3, 1).contiguous()

        # 提取预测值
        x_center = torch.sigmoid(detection[..., 0])
        y_center = torch.sigmoid(detection[..., 1])
        width = detection[..., 2]
        height = detection[..., 3]
        confidence = torch.sigmoid(detection[..., 4])
        class_probs = torch.sigmoid(detection[..., 5:])

        # 生成网格坐标
        grid_x, grid_y = torch.meshgrid(
            torch.arange(grid_w, device=self.device),
            torch.arange(grid_h, device=self.device),
            indexing='xy'
        )

        # 转换为绝对坐标
        x_center = (x_center + grid_x.unsqueeze(0)) / grid_w * self.input_size[1]
        y_center = (y_center + grid_y.unsqueeze(0)) / grid_h * self.input_size[0]

        # 锚框尺寸 (简化版本)
        anchor_sizes = [(10, 13), (16, 30), (33, 23)]  # 示例锚框尺寸
        anchor_w, anchor_h = anchor_sizes[scale_idx % len(anchor_sizes)]

        width = torch.exp(width) * anchor_w
        height = torch.exp(height) * anchor_h

        # 计算边界框
        x1 = x_center - width / 2
        y1 = y_center - height / 2
        x2 = x_center + width / 2
        y2 = y_center + height / 2

        boxes = torch.stack([x1, y1, x2, y2], dim=-1)

        # 计算最终分数
        scores = confidence * class_probs.max(dim=-1)[0]

        # 过滤低置信度检测
        valid_mask = scores > confidence_threshold
        boxes = boxes[valid_mask]
        scores = scores[valid_mask]

        return boxes, scores
