#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于深度学习的车牌识别系统主程序
License Plate Recognition System based on Deep Learning

Author: License Plate Recognition Team
Date: 2025-07-30
Version: 1.0.0
"""

import argparse
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.config_loader import load_config
from src.utils.logger import setup_logger


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description='车牌识别系统')
    parser.add_argument('--mode', type=str, choices=['train', 'test', 'demo', 'collect'], 
                       default='demo', help='运行模式')
    parser.add_argument('--config', type=str, default='config/config.yaml', 
                       help='配置文件路径')
    parser.add_argument('--input', type=str, help='输入图像路径')
    parser.add_argument('--output', type=str, help='输出结果路径')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 设置日志
    logger = setup_logger(config)
    logger.info("车牌识别系统启动")
    logger.info(f"运行模式: {args.mode}")
    
    try:
        if args.mode == 'collect':
            # 数据采集模式
            from src.data_collection.crawler import run_data_collection
            logger.info("开始数据采集...")
            run_data_collection(config)
            
        elif args.mode == 'train':
            # 训练模式
            from src.models.trainer import train_model
            logger.info("开始模型训练...")
            train_model(config)
            
        elif args.mode == 'test':
            # 测试模式
            from src.models.evaluator import evaluate_model
            logger.info("开始模型测试...")
            evaluate_model(config, args.input)
            
        elif args.mode == 'demo':
            # 演示模式
            from src.gui.main_window import run_gui
            logger.info("启动图形界面...")
            run_gui(config)
            
        else:
            logger.error(f"未知的运行模式: {args.mode}")
            return 1
            
    except Exception as e:
        logger.error(f"程序运行出错: {str(e)}")
        return 1
    
    logger.info("程序运行完成")
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
