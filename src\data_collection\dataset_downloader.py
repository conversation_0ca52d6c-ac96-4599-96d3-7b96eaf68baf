#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公开数据集下载器
Public Dataset Downloader

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import os
import zipfile
import tarfile
import requests
from pathlib import Path
from typing import Dict, Any, List
from tqdm import tqdm
import shutil

from ..utils.logger import LoggerMixin


class DatasetDownloader(LoggerMixin):
    """公开数据集下载器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化下载器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.data_config = config.get('data', {})
        self.raw_data_path = Path(self.data_config.get('raw_data_path', 'data/raw'))
        self.raw_data_path.mkdir(parents=True, exist_ok=True)
        
        # 预定义的公开数据集
        self.public_datasets = {
            'ccpd': {
                'name': 'CCPD (Chinese City Parking Dataset)',
                'description': '中国城市停车数据集，包含大量车牌图像',
                'url': 'https://github.com/detectRecog/CCPD',
                'type': 'github',
                'size': '~2GB',
                'format': 'images_with_annotations'
            },
            'openalpr_samples': {
                'name': 'OpenALPR Sample Images',
                'description': 'OpenALPR项目的示例图像',
                'url': 'https://github.com/openalpr/openalpr/tree/master/samples',
                'type': 'github',
                'size': '~50MB',
                'format': 'images_only'
            },
            'synthetic_plates': {
                'name': 'Synthetic License Plates',
                'description': '合成车牌数据集',
                'url': 'custom',
                'type': 'synthetic',
                'size': 'Variable',
                'format': 'images_with_annotations'
            },
            'license_plate_samples': {
                'name': 'License Plate Sample Dataset',
                'description': '车牌样本数据集，包含多种类型的车牌图像',
                'url': 'https://example.com/license-plates.zip',
                'type': 'direct_download',
                'size': '~500MB',
                'format': 'images_with_annotations'
            },
            'chinese_plates_mini': {
                'name': 'Chinese License Plates Mini Dataset',
                'description': '中国车牌迷你数据集，适合快速测试',
                'url': 'https://example.com/chinese-plates-mini.zip',
                'type': 'direct_download',
                'size': '~100MB',
                'format': 'images_with_annotations'
            }
        }
        
        self.logger.info("数据集下载器初始化完成")
    
    def list_available_datasets(self) -> None:
        """列出可用的数据集"""
        self.logger.info("可用的公开数据集:")
        for key, dataset in self.public_datasets.items():
            print(f"\n{key}:")
            print(f"  名称: {dataset['name']}")
            print(f"  描述: {dataset['description']}")
            print(f"  大小: {dataset['size']}")
            print(f"  类型: {dataset['type']}")
    
    def download_dataset(self, dataset_key: str) -> bool:
        """
        下载指定数据集
        
        Args:
            dataset_key (str): 数据集键名
            
        Returns:
            bool: 是否下载成功
        """
        if dataset_key not in self.public_datasets:
            self.logger.error(f"未知的数据集: {dataset_key}")
            return False
        
        dataset_info = self.public_datasets[dataset_key]
        self.logger.info(f"开始下载数据集: {dataset_info['name']}")
        
        try:
            if dataset_info['type'] == 'github':
                return self._download_github_dataset(dataset_key, dataset_info)
            elif dataset_info['type'] == 'synthetic':
                return self._generate_synthetic_dataset(dataset_key, dataset_info)
            elif dataset_info['type'] == 'direct_download':
                return self._download_direct_dataset(dataset_key, dataset_info)
            else:
                self.logger.error(f"不支持的数据集类型: {dataset_info['type']}")
                return False

        except Exception as e:
            self.logger.error(f"下载数据集失败: {str(e)}")
            return False
    
    def _download_github_dataset(self, dataset_key: str, dataset_info: Dict[str, Any]) -> bool:
        """
        从GitHub下载数据集
        
        Args:
            dataset_key (str): 数据集键名
            dataset_info (Dict[str, Any]): 数据集信息
            
        Returns:
            bool: 是否下载成功
        """
        # 这里实现GitHub数据集下载逻辑
        # 由于直接从GitHub下载大型数据集可能有限制，
        # 这里提供一个框架，实际使用时需要根据具体情况调整
        
        self.logger.info(f"GitHub数据集下载: {dataset_info['url']}")
        self.logger.warning("GitHub数据集下载需要手动处理，请访问以下链接:")
        self.logger.warning(dataset_info['url'])
        
        # 创建数据集目录
        dataset_dir = self.raw_data_path / dataset_key
        dataset_dir.mkdir(exist_ok=True)
        
        # 创建说明文件
        readme_content = f"""# {dataset_info['name']}

{dataset_info['description']}

数据集链接: {dataset_info['url']}
预估大小: {dataset_info['size']}

请手动下载数据集并解压到当前目录。
"""
        
        with open(dataset_dir / 'README.md', 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        return True
    
    def _generate_synthetic_dataset(self, dataset_key: str, dataset_info: Dict[str, Any]) -> bool:
        """
        生成合成数据集
        
        Args:
            dataset_key (str): 数据集键名
            dataset_info (Dict[str, Any]): 数据集信息
            
        Returns:
            bool: 是否生成成功
        """
        from .synthetic_generator import SyntheticPlateGenerator
        
        self.logger.info("开始生成合成车牌数据集")
        
        # 创建数据集目录
        dataset_dir = self.raw_data_path / dataset_key
        dataset_dir.mkdir(exist_ok=True)
        
        # 初始化合成数据生成器
        generator = SyntheticPlateGenerator(self.config)
        
        # 从配置中读取样本数量
        num_samples = self.config.get('data_collection', {}).get('synthetic_samples', 3000)
        include_scenes = self.config.get('data_collection', {}).get('include_scenes', True)

        # 生成合成数据
        success_count = generator.generate_dataset(dataset_dir, num_samples, include_scenes)

        self.logger.info(f"合成数据集生成完成，成功生成 {success_count} 个样本")
        return success_count > 0

    def _download_direct_dataset(self, dataset_key: str, dataset_info: Dict[str, Any]) -> bool:
        """
        直接下载数据集

        Args:
            dataset_key (str): 数据集键名
            dataset_info (Dict[str, Any]): 数据集信息

        Returns:
            bool: 是否下载成功
        """
        self.logger.info(f"开始下载数据集: {dataset_info['name']}")

        # 创建数据集目录
        dataset_dir = self.raw_data_path / dataset_key
        dataset_dir.mkdir(exist_ok=True)

        # 下载文件
        url = dataset_info['url']
        filename = dataset_dir / f"{dataset_key}.zip"

        # 检查是否已经下载
        if filename.exists():
            self.logger.info(f"数据集文件已存在: {filename}")
            # 检查是否已解压
            extracted_dir = dataset_dir / 'extracted'
            if extracted_dir.exists() and any(extracted_dir.iterdir()):
                self.logger.info("数据集已解压，跳过下载")
                return True

        try:
            # 下载文件
            success = self.download_file(url, str(filename))
            if not success:
                return False

            # 解压文件
            extracted_dir = dataset_dir / 'extracted'
            success = self.extract_archive(str(filename), str(extracted_dir))
            if not success:
                return False

            # 验证下载的数据
            validation_result = self._validate_downloaded_dataset(extracted_dir, dataset_info)
            if not validation_result['valid']:
                self.logger.error(f"数据集验证失败: {validation_result['error']}")
                return False

            # 生成数据集信息文件
            self._generate_download_info(dataset_dir, dataset_info, validation_result)

            self.logger.info(f"数据集下载完成: {dataset_info['name']}")
            return True

        except Exception as e:
            self.logger.error(f"下载数据集失败: {str(e)}")
            return False

    def _validate_downloaded_dataset(self, dataset_dir: Path, dataset_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证下载的数据集

        Args:
            dataset_dir (Path): 数据集目录
            dataset_info (Dict[str, Any]): 数据集信息

        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            'valid': False,
            'error': None,
            'stats': {}
        }

        try:
            # 检查目录是否存在
            if not dataset_dir.exists():
                result['error'] = "数据集目录不存在"
                return result

            # 统计图像文件
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
            image_files = []
            for ext in image_extensions:
                image_files.extend(list(dataset_dir.glob(f'**/*{ext}')))
                image_files.extend(list(dataset_dir.glob(f'**/*{ext.upper()}')))

            if len(image_files) == 0:
                result['error'] = "未找到图像文件"
                return result

            # 统计标注文件（如果需要）
            annotation_files = []
            if dataset_info.get('format') == 'images_with_annotations':
                annotation_files = list(dataset_dir.glob('**/*.json'))
                annotation_files.extend(list(dataset_dir.glob('**/*.xml')))
                annotation_files.extend(list(dataset_dir.glob('**/*.txt')))

            result['stats'] = {
                'total_images': len(image_files),
                'total_annotations': len(annotation_files),
                'image_extensions': list(set([f.suffix.lower() for f in image_files])),
                'dataset_size_mb': sum(f.stat().st_size for f in dataset_dir.glob('**/*') if f.is_file()) / (1024 * 1024)
            }

            result['valid'] = True
            return result

        except Exception as e:
            result['error'] = f"验证过程出错: {str(e)}"
            return result

    def _generate_download_info(self, dataset_dir: Path, dataset_info: Dict[str, Any], validation_result: Dict[str, Any]) -> None:
        """
        生成下载信息文件

        Args:
            dataset_dir (Path): 数据集目录
            dataset_info (Dict[str, Any]): 数据集信息
            validation_result (Dict[str, Any]): 验证结果
        """
        from datetime import datetime

        download_info = {
            'dataset_name': dataset_info['name'],
            'dataset_key': dataset_dir.name,
            'description': dataset_info['description'],
            'source_url': dataset_info['url'],
            'download_date': datetime.now().isoformat(),
            'dataset_type': dataset_info['type'],
            'format': dataset_info.get('format', 'unknown'),
            'estimated_size': dataset_info.get('size', 'unknown'),
            'validation_stats': validation_result.get('stats', {}),
            'status': 'downloaded_and_validated'
        }

        info_path = dataset_dir / 'download_info.json'
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(download_info, f, ensure_ascii=False, indent=2)

        # 生成README
        readme_content = f"""# {dataset_info['name']}

## 数据集信息
- **名称**: {dataset_info['name']}
- **描述**: {dataset_info['description']}
- **来源**: {dataset_info['url']}
- **下载日期**: {download_info['download_date']}
- **格式**: {dataset_info.get('format', 'unknown')}

## 统计信息
- **图像数量**: {validation_result.get('stats', {}).get('total_images', 'unknown')}
- **标注数量**: {validation_result.get('stats', {}).get('total_annotations', 'unknown')}
- **数据集大小**: {validation_result.get('stats', {}).get('dataset_size_mb', 0):.2f} MB

## 目录结构
```
{dataset_dir.name}/
├── extracted/          # 解压后的数据文件
├── download_info.json  # 下载信息
├── README.md          # 说明文档
└── {dataset_dir.name}.zip      # 原始压缩文件
```

## 使用说明
1. 主要数据文件位于 `extracted/` 目录中
2. 根据数据集格式，可能包含图像和标注文件
3. 请参考原始数据集文档了解具体的数据格式和使用方法
"""

        readme_path = dataset_dir / 'README.md'
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
    
    def download_file(self, url: str, filename: str, chunk_size: int = 8192) -> bool:
        """
        下载文件
        
        Args:
            url (str): 文件URL
            filename (str): 保存的文件名
            chunk_size (int): 块大小
            
        Returns:
            bool: 是否下载成功
        """
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            
            with open(filename, 'wb') as f, tqdm(
                desc=f"下载 {Path(filename).name}",
                total=total_size,
                unit='B',
                unit_scale=True,
                unit_divisor=1024,
            ) as pbar:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        pbar.update(len(chunk))
            
            self.logger.info(f"文件下载完成: {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"文件下载失败 {url}: {str(e)}")
            return False
    
    def extract_archive(self, archive_path: str, extract_to: str) -> bool:
        """
        解压缩文件
        
        Args:
            archive_path (str): 压缩文件路径
            extract_to (str): 解压目标路径
            
        Returns:
            bool: 是否解压成功
        """
        try:
            archive_path = Path(archive_path)
            extract_to = Path(extract_to)
            extract_to.mkdir(parents=True, exist_ok=True)
            
            if archive_path.suffix.lower() == '.zip':
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_to)
            elif archive_path.suffix.lower() in ['.tar', '.tar.gz', '.tgz']:
                with tarfile.open(archive_path, 'r:*') as tar_ref:
                    tar_ref.extractall(extract_to)
            else:
                self.logger.error(f"不支持的压缩格式: {archive_path.suffix}")
                return False
            
            self.logger.info(f"文件解压完成: {archive_path} -> {extract_to}")
            return True
            
        except Exception as e:
            self.logger.error(f"文件解压失败: {str(e)}")
            return False
    
    def cleanup_downloads(self) -> None:
        """清理下载的临时文件"""
        temp_files = list(self.raw_data_path.glob('*.zip'))
        temp_files.extend(list(self.raw_data_path.glob('*.tar*')))
        
        for temp_file in temp_files:
            try:
                temp_file.unlink()
                self.logger.info(f"删除临时文件: {temp_file}")
            except Exception as e:
                self.logger.warning(f"删除临时文件失败 {temp_file}: {str(e)}")


def download_public_datasets(config: Dict[str, Any], dataset_keys: List[str] = None) -> None:
    """
    下载公开数据集
    
    Args:
        config (Dict[str, Any]): 配置字典
        dataset_keys (List[str]): 要下载的数据集键名列表，None表示下载所有
    """
    downloader = DatasetDownloader(config)
    
    if dataset_keys is None:
        # 列出可用数据集
        downloader.list_available_datasets()
        return
    
    for dataset_key in dataset_keys:
        success = downloader.download_dataset(dataset_key)
        if success:
            downloader.logger.info(f"数据集 {dataset_key} 下载成功")
        else:
            downloader.logger.error(f"数据集 {dataset_key} 下载失败")
    
    # 清理临时文件
    downloader.cleanup_downloads()
