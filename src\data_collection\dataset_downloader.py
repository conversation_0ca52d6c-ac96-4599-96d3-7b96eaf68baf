#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公开数据集下载器
Public Dataset Downloader

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import os
import zipfile
import tarfile
import requests
from pathlib import Path
from typing import Dict, Any, List
from tqdm import tqdm
import shutil

from ..utils.logger import LoggerMixin


class DatasetDownloader(LoggerMixin):
    """公开数据集下载器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化下载器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.data_config = config.get('data', {})
        self.raw_data_path = Path(self.data_config.get('raw_data_path', 'data/raw'))
        self.raw_data_path.mkdir(parents=True, exist_ok=True)
        
        # 预定义的公开数据集
        self.public_datasets = {
            'ccpd': {
                'name': 'CCPD (Chinese City Parking Dataset)',
                'description': '中国城市停车数据集，包含大量车牌图像',
                'url': 'https://github.com/detectRecog/CCPD',
                'type': 'github',
                'size': '~2GB'
            },
            'openalpr_samples': {
                'name': 'OpenALPR Sample Images',
                'description': 'OpenALPR项目的示例图像',
                'url': 'https://github.com/openalpr/openalpr/tree/master/samples',
                'type': 'github',
                'size': '~50MB'
            },
            'synthetic_plates': {
                'name': 'Synthetic License Plates',
                'description': '合成车牌数据集',
                'url': 'custom',
                'type': 'synthetic',
                'size': 'Variable'
            }
        }
        
        self.logger.info("数据集下载器初始化完成")
    
    def list_available_datasets(self) -> None:
        """列出可用的数据集"""
        self.logger.info("可用的公开数据集:")
        for key, dataset in self.public_datasets.items():
            print(f"\n{key}:")
            print(f"  名称: {dataset['name']}")
            print(f"  描述: {dataset['description']}")
            print(f"  大小: {dataset['size']}")
            print(f"  类型: {dataset['type']}")
    
    def download_dataset(self, dataset_key: str) -> bool:
        """
        下载指定数据集
        
        Args:
            dataset_key (str): 数据集键名
            
        Returns:
            bool: 是否下载成功
        """
        if dataset_key not in self.public_datasets:
            self.logger.error(f"未知的数据集: {dataset_key}")
            return False
        
        dataset_info = self.public_datasets[dataset_key]
        self.logger.info(f"开始下载数据集: {dataset_info['name']}")
        
        try:
            if dataset_info['type'] == 'github':
                return self._download_github_dataset(dataset_key, dataset_info)
            elif dataset_info['type'] == 'synthetic':
                return self._generate_synthetic_dataset(dataset_key, dataset_info)
            else:
                self.logger.error(f"不支持的数据集类型: {dataset_info['type']}")
                return False
                
        except Exception as e:
            self.logger.error(f"下载数据集失败: {str(e)}")
            return False
    
    def _download_github_dataset(self, dataset_key: str, dataset_info: Dict[str, Any]) -> bool:
        """
        从GitHub下载数据集
        
        Args:
            dataset_key (str): 数据集键名
            dataset_info (Dict[str, Any]): 数据集信息
            
        Returns:
            bool: 是否下载成功
        """
        # 这里实现GitHub数据集下载逻辑
        # 由于直接从GitHub下载大型数据集可能有限制，
        # 这里提供一个框架，实际使用时需要根据具体情况调整
        
        self.logger.info(f"GitHub数据集下载: {dataset_info['url']}")
        self.logger.warning("GitHub数据集下载需要手动处理，请访问以下链接:")
        self.logger.warning(dataset_info['url'])
        
        # 创建数据集目录
        dataset_dir = self.raw_data_path / dataset_key
        dataset_dir.mkdir(exist_ok=True)
        
        # 创建说明文件
        readme_content = f"""# {dataset_info['name']}

{dataset_info['description']}

数据集链接: {dataset_info['url']}
预估大小: {dataset_info['size']}

请手动下载数据集并解压到当前目录。
"""
        
        with open(dataset_dir / 'README.md', 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        return True
    
    def _generate_synthetic_dataset(self, dataset_key: str, dataset_info: Dict[str, Any]) -> bool:
        """
        生成合成数据集
        
        Args:
            dataset_key (str): 数据集键名
            dataset_info (Dict[str, Any]): 数据集信息
            
        Returns:
            bool: 是否生成成功
        """
        from .synthetic_generator import SyntheticPlateGenerator
        
        self.logger.info("开始生成合成车牌数据集")
        
        # 创建数据集目录
        dataset_dir = self.raw_data_path / dataset_key
        dataset_dir.mkdir(exist_ok=True)
        
        # 初始化合成数据生成器
        generator = SyntheticPlateGenerator(self.config)
        
        # 生成合成数据
        num_samples = 1000  # 可以从配置中读取
        success_count = generator.generate_dataset(dataset_dir, num_samples)
        
        self.logger.info(f"合成数据集生成完成，成功生成 {success_count} 个样本")
        return success_count > 0
    
    def download_file(self, url: str, filename: str, chunk_size: int = 8192) -> bool:
        """
        下载文件
        
        Args:
            url (str): 文件URL
            filename (str): 保存的文件名
            chunk_size (int): 块大小
            
        Returns:
            bool: 是否下载成功
        """
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            
            with open(filename, 'wb') as f, tqdm(
                desc=f"下载 {Path(filename).name}",
                total=total_size,
                unit='B',
                unit_scale=True,
                unit_divisor=1024,
            ) as pbar:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        pbar.update(len(chunk))
            
            self.logger.info(f"文件下载完成: {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"文件下载失败 {url}: {str(e)}")
            return False
    
    def extract_archive(self, archive_path: str, extract_to: str) -> bool:
        """
        解压缩文件
        
        Args:
            archive_path (str): 压缩文件路径
            extract_to (str): 解压目标路径
            
        Returns:
            bool: 是否解压成功
        """
        try:
            archive_path = Path(archive_path)
            extract_to = Path(extract_to)
            extract_to.mkdir(parents=True, exist_ok=True)
            
            if archive_path.suffix.lower() == '.zip':
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_to)
            elif archive_path.suffix.lower() in ['.tar', '.tar.gz', '.tgz']:
                with tarfile.open(archive_path, 'r:*') as tar_ref:
                    tar_ref.extractall(extract_to)
            else:
                self.logger.error(f"不支持的压缩格式: {archive_path.suffix}")
                return False
            
            self.logger.info(f"文件解压完成: {archive_path} -> {extract_to}")
            return True
            
        except Exception as e:
            self.logger.error(f"文件解压失败: {str(e)}")
            return False
    
    def cleanup_downloads(self) -> None:
        """清理下载的临时文件"""
        temp_files = list(self.raw_data_path.glob('*.zip'))
        temp_files.extend(list(self.raw_data_path.glob('*.tar*')))
        
        for temp_file in temp_files:
            try:
                temp_file.unlink()
                self.logger.info(f"删除临时文件: {temp_file}")
            except Exception as e:
                self.logger.warning(f"删除临时文件失败 {temp_file}: {str(e)}")


def download_public_datasets(config: Dict[str, Any], dataset_keys: List[str] = None) -> None:
    """
    下载公开数据集
    
    Args:
        config (Dict[str, Any]): 配置字典
        dataset_keys (List[str]): 要下载的数据集键名列表，None表示下载所有
    """
    downloader = DatasetDownloader(config)
    
    if dataset_keys is None:
        # 列出可用数据集
        downloader.list_available_datasets()
        return
    
    for dataset_key in dataset_keys:
        success = downloader.download_dataset(dataset_key)
        if success:
            downloader.logger.info(f"数据集 {dataset_key} 下载成功")
        else:
            downloader.logger.error(f"数据集 {dataset_key} 下载失败")
    
    # 清理临时文件
    downloader.cleanup_downloads()
