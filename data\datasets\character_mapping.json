{"char_to_idx": {"<UNKNOWN>": 0, "京": 1, "津": 2, "沪": 3, "渝": 4, "冀": 5, "豫": 6, "云": 7, "辽": 8, "黑": 9, "湘": 10, "皖": 11, "鲁": 12, "新": 13, "苏": 14, "浙": 15, "赣": 16, "鄂": 17, "桂": 18, "甘": 19, "晋": 20, "蒙": 21, "陕": 22, "吉": 23, "闽": 24, "贵": 25, "粤": 26, "青": 27, "藏": 28, "川": 29, "宁": 30, "琼": 31, "A": 32, "B": 33, "C": 34, "D": 35, "E": 36, "F": 37, "G": 38, "H": 39, "J": 40, "K": 41, "L": 42, "M": 43, "N": 44, "P": 45, "Q": 46, "R": 47, "S": 48, "T": 49, "U": 50, "V": 51, "W": 52, "X": 53, "Y": 54, "Z": 55, "0": 56, "1": 57, "2": 58, "3": 59, "4": 60, "5": 61, "6": 62, "7": 63, "8": 64, "9": 65}, "idx_to_char": {"0": "<UNKNOWN>", "1": "京", "2": "津", "3": "沪", "4": "渝", "5": "冀", "6": "豫", "7": "云", "8": "辽", "9": "黑", "10": "湘", "11": "皖", "12": "鲁", "13": "新", "14": "苏", "15": "浙", "16": "赣", "17": "鄂", "18": "桂", "19": "甘", "20": "晋", "21": "蒙", "22": "陕", "23": "吉", "24": "闽", "25": "贵", "26": "粤", "27": "青", "28": "藏", "29": "川", "30": "宁", "31": "琼", "32": "A", "33": "B", "34": "C", "35": "D", "36": "E", "37": "F", "38": "G", "39": "H", "40": "J", "41": "K", "42": "L", "43": "M", "44": "N", "45": "P", "46": "Q", "47": "R", "48": "S", "49": "T", "50": "U", "51": "V", "52": "W", "53": "X", "54": "Y", "55": "Z", "56": "0", "57": "1", "58": "2", "59": "3", "60": "4", "61": "5", "62": "6", "63": "7", "64": "8", "65": "9"}, "num_classes": 66, "description": "Character mapping for license plate recognition"}