#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字符分割器
Character Segmenter

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import cv2
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path
import matplotlib.pyplot as plt

from ..utils.logger import LoggerMixin
from .segmentation_utils import SegmentationUtils


class CharacterSegmenter(LoggerMixin):
    """车牌字符分割器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化字符分割器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.seg_config = config.get('segmentation', {})
        
        # 分割参数
        self.min_char_width = self.seg_config.get('min_char_width', 8)
        self.max_char_width = self.seg_config.get('max_char_width', 50)
        self.min_char_height = self.seg_config.get('min_char_height', 15)
        self.char_width_ratio = self.seg_config.get('char_width_ratio', 0.3)
        
        # 工具类
        self.utils = SegmentationUtils()
        
        self.logger.info("字符分割器初始化完成")
    
    def segment_characters(self, plate_image: np.ndarray, 
                          debug: bool = False) -> List[np.ndarray]:
        """
        分割车牌字符
        
        Args:
            plate_image (np.ndarray): 车牌图像
            debug (bool): 是否启用调试模式
            
        Returns:
            List[np.ndarray]: 分割后的字符图像列表
        """
        if plate_image is None or plate_image.size == 0:
            self.logger.warning("输入的车牌图像为空")
            return []
        
        try:
            # 预处理
            processed_image = self._preprocess_plate(plate_image)
            
            # 垂直投影分割
            char_regions = self._vertical_projection_segmentation(processed_image)
            
            # 后处理和验证
            valid_regions = self._validate_char_regions(char_regions, processed_image.shape)
            
            # 提取字符图像
            char_images = self._extract_char_images(plate_image, valid_regions)
            
            if debug:
                self._debug_visualization(plate_image, processed_image, 
                                        char_regions, valid_regions)
            
            self.logger.info(f"成功分割出 {len(char_images)} 个字符")
            return char_images
            
        except Exception as e:
            self.logger.error(f"字符分割失败: {str(e)}")
            return []
    
    def _preprocess_plate(self, plate_image: np.ndarray) -> np.ndarray:
        """
        预处理车牌图像
        
        Args:
            plate_image (np.ndarray): 原始车牌图像
            
        Returns:
            np.ndarray: 预处理后的图像
        """
        # 转换为灰度图
        if len(plate_image.shape) == 3:
            gray = cv2.cvtColor(plate_image, cv2.COLOR_BGR2GRAY)
        else:
            gray = plate_image.copy()
        
        # 高斯模糊去噪
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # 自适应阈值二值化
        binary = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY_INV, 11, 2
        )
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return binary
    
    def _vertical_projection_segmentation(self, binary_image: np.ndarray) -> List[Tuple[int, int]]:
        """
        基于垂直投影的字符分割
        
        Args:
            binary_image (np.ndarray): 二值化图像
            
        Returns:
            List[Tuple[int, int]]: 字符区域列表 [(start_x, end_x), ...]
        """
        # 计算垂直投影
        vertical_projection = np.sum(binary_image, axis=0)
        
        # 平滑投影曲线
        kernel_size = max(3, binary_image.shape[1] // 50)
        if kernel_size % 2 == 0:
            kernel_size += 1
        
        smoothed_projection = cv2.GaussianBlur(
            vertical_projection.astype(np.float32).reshape(1, -1), 
            (kernel_size, 1), 0
        ).flatten()
        
        # 寻找分割点
        char_regions = self._find_char_boundaries(smoothed_projection)
        
        return char_regions
    
    def _find_char_boundaries(self, projection: np.ndarray) -> List[Tuple[int, int]]:
        """
        寻找字符边界
        
        Args:
            projection (np.ndarray): 垂直投影数组
            
        Returns:
            List[Tuple[int, int]]: 字符边界列表
        """
        # 计算阈值
        threshold = np.mean(projection) * 0.3
        
        # 寻找字符区域
        char_regions = []
        in_char = False
        start_x = 0
        
        for x, value in enumerate(projection):
            if not in_char and value > threshold:
                # 字符开始
                in_char = True
                start_x = x
            elif in_char and value <= threshold:
                # 字符结束
                in_char = False
                if x - start_x >= self.min_char_width:
                    char_regions.append((start_x, x))
        
        # 处理最后一个字符
        if in_char and len(projection) - start_x >= self.min_char_width:
            char_regions.append((start_x, len(projection)))
        
        return char_regions
    
    def _validate_char_regions(self, char_regions: List[Tuple[int, int]], 
                              image_shape: Tuple[int, int]) -> List[Tuple[int, int]]:
        """
        验证和优化字符区域
        
        Args:
            char_regions (List[Tuple[int, int]]): 原始字符区域
            image_shape (Tuple[int, int]): 图像尺寸
            
        Returns:
            List[Tuple[int, int]]: 验证后的字符区域
        """
        if not char_regions:
            return []
        
        height, width = image_shape
        valid_regions = []
        
        for start_x, end_x in char_regions:
            char_width = end_x - start_x
            
            # 检查宽度范围
            if char_width < self.min_char_width or char_width > self.max_char_width:
                continue
            
            # 检查宽高比
            char_height = height
            aspect_ratio = char_width / char_height
            if aspect_ratio < 0.1 or aspect_ratio > 2.0:
                continue
            
            valid_regions.append((start_x, end_x))
        
        # 合并过近的区域
        valid_regions = self._merge_close_regions(valid_regions, width)
        
        # 分割过宽的区域
        valid_regions = self._split_wide_regions(valid_regions, height)
        
        return valid_regions
    
    def _merge_close_regions(self, regions: List[Tuple[int, int]], 
                           image_width: int) -> List[Tuple[int, int]]:
        """
        合并过近的字符区域
        
        Args:
            regions (List[Tuple[int, int]]): 字符区域列表
            image_width (int): 图像宽度
            
        Returns:
            List[Tuple[int, int]]: 合并后的区域列表
        """
        if len(regions) <= 1:
            return regions
        
        merged_regions = []
        current_start, current_end = regions[0]
        
        for i in range(1, len(regions)):
            next_start, next_end = regions[i]
            
            # 计算间距
            gap = next_start - current_end
            min_gap = image_width * 0.02  # 最小间距阈值
            
            if gap < min_gap:
                # 合并区域
                current_end = next_end
            else:
                # 保存当前区域，开始新区域
                merged_regions.append((current_start, current_end))
                current_start, current_end = next_start, next_end
        
        # 添加最后一个区域
        merged_regions.append((current_start, current_end))
        
        return merged_regions
    
    def _split_wide_regions(self, regions: List[Tuple[int, int]], 
                          image_height: int) -> List[Tuple[int, int]]:
        """
        分割过宽的字符区域
        
        Args:
            regions (List[Tuple[int, int]]): 字符区域列表
            image_height (int): 图像高度
            
        Returns:
            List[Tuple[int, int]]: 分割后的区域列表
        """
        split_regions = []
        expected_char_width = image_height * self.char_width_ratio
        
        for start_x, end_x in regions:
            char_width = end_x - start_x
            
            # 如果区域过宽，尝试分割
            if char_width > expected_char_width * 1.8:
                # 估计字符数量
                num_chars = round(char_width / expected_char_width)
                if num_chars > 1:
                    # 均匀分割
                    char_width_avg = char_width / num_chars
                    for i in range(num_chars):
                        split_start = int(start_x + i * char_width_avg)
                        split_end = int(start_x + (i + 1) * char_width_avg)
                        split_regions.append((split_start, split_end))
                else:
                    split_regions.append((start_x, end_x))
            else:
                split_regions.append((start_x, end_x))
        
        return split_regions
    
    def _extract_char_images(self, original_image: np.ndarray, 
                           char_regions: List[Tuple[int, int]]) -> List[np.ndarray]:
        """
        提取字符图像
        
        Args:
            original_image (np.ndarray): 原始车牌图像
            char_regions (List[Tuple[int, int]]): 字符区域列表
            
        Returns:
            List[np.ndarray]: 字符图像列表
        """
        char_images = []
        
        for start_x, end_x in char_regions:
            # 确保坐标在有效范围内
            start_x = max(0, start_x)
            end_x = min(original_image.shape[1], end_x)
            
            if start_x >= end_x:
                continue
            
            # 提取字符区域
            char_image = original_image[:, start_x:end_x]
            
            # 添加边距
            char_image = self._add_padding(char_image)
            
            # 标准化尺寸
            char_image = self._normalize_char_size(char_image)
            
            char_images.append(char_image)
        
        return char_images
    
    def _add_padding(self, char_image: np.ndarray, padding_ratio: float = 0.1) -> np.ndarray:
        """
        为字符图像添加边距
        
        Args:
            char_image (np.ndarray): 字符图像
            padding_ratio (float): 边距比例
            
        Returns:
            np.ndarray: 添加边距后的图像
        """
        height, width = char_image.shape[:2]
        
        # 计算边距
        pad_h = int(height * padding_ratio)
        pad_w = int(width * padding_ratio)
        
        # 添加边距
        if len(char_image.shape) == 3:
            padded = cv2.copyMakeBorder(
                char_image, pad_h, pad_h, pad_w, pad_w, 
                cv2.BORDER_CONSTANT, value=(255, 255, 255)
            )
        else:
            padded = cv2.copyMakeBorder(
                char_image, pad_h, pad_h, pad_w, pad_w, 
                cv2.BORDER_CONSTANT, value=255
            )
        
        return padded
    
    def _normalize_char_size(self, char_image: np.ndarray, 
                           target_size: Tuple[int, int] = (64, 64)) -> np.ndarray:
        """
        标准化字符图像尺寸
        
        Args:
            char_image (np.ndarray): 字符图像
            target_size (Tuple[int, int]): 目标尺寸
            
        Returns:
            np.ndarray: 标准化后的图像
        """
        # 保持宽高比的缩放
        height, width = char_image.shape[:2]
        target_h, target_w = target_size
        
        # 计算缩放比例
        scale = min(target_w / width, target_h / height)
        
        # 计算新尺寸
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        # 缩放图像
        resized = cv2.resize(char_image, (new_width, new_height), 
                           interpolation=cv2.INTER_AREA)
        
        # 创建目标尺寸的画布
        if len(char_image.shape) == 3:
            canvas = np.full((target_h, target_w, char_image.shape[2]), 255, dtype=np.uint8)
        else:
            canvas = np.full((target_h, target_w), 255, dtype=np.uint8)
        
        # 计算居中位置
        y_offset = (target_h - new_height) // 2
        x_offset = (target_w - new_width) // 2
        
        # 将缩放后的图像放置在画布中心
        if len(char_image.shape) == 3:
            canvas[y_offset:y_offset+new_height, x_offset:x_offset+new_width] = resized
        else:
            canvas[y_offset:y_offset+new_height, x_offset:x_offset+new_width] = resized
        
        return canvas
    
    def _debug_visualization(self, original_image: np.ndarray, 
                           processed_image: np.ndarray,
                           char_regions: List[Tuple[int, int]], 
                           valid_regions: List[Tuple[int, int]]) -> None:
        """
        调试可视化
        
        Args:
            original_image (np.ndarray): 原始图像
            processed_image (np.ndarray): 处理后图像
            char_regions (List[Tuple[int, int]]): 原始字符区域
            valid_regions (List[Tuple[int, int]]): 有效字符区域
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 8))
        
        # 原始图像
        axes[0, 0].imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
        axes[0, 0].set_title('原始车牌图像')
        axes[0, 0].axis('off')
        
        # 预处理图像
        axes[0, 1].imshow(processed_image, cmap='gray')
        axes[0, 1].set_title('预处理图像')
        axes[0, 1].axis('off')
        
        # 垂直投影
        vertical_projection = np.sum(processed_image, axis=0)
        axes[1, 0].plot(vertical_projection)
        axes[1, 0].set_title('垂直投影')
        axes[1, 0].set_xlabel('X坐标')
        axes[1, 0].set_ylabel('投影值')
        
        # 分割结果
        result_image = original_image.copy()
        for i, (start_x, end_x) in enumerate(valid_regions):
            cv2.rectangle(result_image, (start_x, 0), (end_x, result_image.shape[0]), 
                         (0, 255, 0), 2)
            cv2.putText(result_image, str(i), (start_x, 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        axes[1, 1].imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
        axes[1, 1].set_title(f'分割结果 ({len(valid_regions)}个字符)')
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        plt.show()


class AdaptiveCharacterSegmenter(CharacterSegmenter):
    """自适应字符分割器"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化自适应字符分割器

        Args:
            config (Dict[str, Any]): 配置字典
        """
        super(AdaptiveCharacterSegmenter, self).__init__(config)

        # 自适应参数
        self.use_contour_analysis = self.seg_config.get('use_contour_analysis', True)
        self.use_watershed = self.seg_config.get('use_watershed', True)
        self.min_contour_area = self.seg_config.get('min_contour_area', 50)

        self.logger.info("自适应字符分割器初始化完成")

    def segment_characters(self, plate_image: np.ndarray,
                          debug: bool = False) -> List[np.ndarray]:
        """
        自适应分割车牌字符

        Args:
            plate_image (np.ndarray): 车牌图像
            debug (bool): 是否启用调试模式

        Returns:
            List[np.ndarray]: 分割后的字符图像列表
        """
        if plate_image is None or plate_image.size == 0:
            self.logger.warning("输入的车牌图像为空")
            return []

        try:
            # 预处理
            processed_image = self._preprocess_plate(plate_image)

            # 尝试多种分割方法
            methods_results = []

            # 方法1: 垂直投影分割
            projection_regions = self._vertical_projection_segmentation(processed_image)
            projection_chars = self._extract_char_images(plate_image, projection_regions)
            methods_results.append(('projection', projection_chars))

            # 方法2: 轮廓分析分割
            if self.use_contour_analysis:
                contour_regions = self._contour_based_segmentation(processed_image)
                contour_chars = self._extract_char_images(plate_image, contour_regions)
                methods_results.append(('contour', contour_chars))

            # 方法3: 分水岭分割
            if self.use_watershed:
                watershed_regions = self._watershed_segmentation(processed_image)
                watershed_chars = self._extract_char_images(plate_image, watershed_regions)
                methods_results.append(('watershed', watershed_chars))

            # 选择最佳结果
            best_chars = self._select_best_segmentation(methods_results, plate_image)

            if debug:
                self._debug_adaptive_visualization(plate_image, processed_image, methods_results)

            self.logger.info(f"自适应分割成功分割出 {len(best_chars)} 个字符")
            return best_chars

        except Exception as e:
            self.logger.error(f"自适应字符分割失败: {str(e)}")
            return []

    def _contour_based_segmentation(self, binary_image: np.ndarray) -> List[Tuple[int, int]]:
        """
        基于轮廓的字符分割

        Args:
            binary_image (np.ndarray): 二值化图像

        Returns:
            List[Tuple[int, int]]: 字符区域列表
        """
        # 寻找轮廓
        contours, _ = cv2.findContours(binary_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 过滤和排序轮廓
        valid_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > self.min_contour_area:
                x, y, w, h = cv2.boundingRect(contour)
                # 检查宽高比和尺寸
                if (w >= self.min_char_width and w <= self.max_char_width and
                    h >= self.min_char_height and 0.1 <= w/h <= 2.0):
                    valid_contours.append((x, x + w, contour))

        # 按x坐标排序
        valid_contours.sort(key=lambda x: x[0])

        # 提取区域
        char_regions = [(start_x, end_x) for start_x, end_x, _ in valid_contours]

        return char_regions

    def _watershed_segmentation(self, binary_image: np.ndarray) -> List[Tuple[int, int]]:
        """
        基于分水岭算法的字符分割

        Args:
            binary_image (np.ndarray): 二值化图像

        Returns:
            List[Tuple[int, int]]: 字符区域列表
        """
        # 距离变换
        dist_transform = cv2.distanceTransform(binary_image, cv2.DIST_L2, 5)

        # 寻找局部最大值作为种子点
        local_maxima = cv2.dilate(dist_transform, np.ones((3, 3), np.uint8))
        local_maxima = (dist_transform == local_maxima) & (dist_transform > 0.3 * dist_transform.max())

        # 标记种子点
        markers = cv2.connectedComponents(local_maxima.astype(np.uint8))[1]

        # 应用分水岭算法
        markers = cv2.watershed(cv2.cvtColor(binary_image, cv2.COLOR_GRAY2BGR), markers)

        # 提取分割区域
        char_regions = []
        for label in np.unique(markers):
            if label <= 1:  # 跳过背景和边界
                continue

            # 获取当前标签的区域
            mask = (markers == label).astype(np.uint8)
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if contours:
                x, y, w, h = cv2.boundingRect(contours[0])
                if (w >= self.min_char_width and w <= self.max_char_width and
                    h >= self.min_char_height):
                    char_regions.append((x, x + w))

        # 排序
        char_regions.sort(key=lambda x: x[0])

        return char_regions

    def _select_best_segmentation(self, methods_results: List[Tuple[str, List[np.ndarray]]],
                                 original_image: np.ndarray) -> List[np.ndarray]:
        """
        选择最佳分割结果

        Args:
            methods_results (List[Tuple[str, List[np.ndarray]]]): 各方法的分割结果
            original_image (np.ndarray): 原始图像

        Returns:
            List[np.ndarray]: 最佳分割结果
        """
        if not methods_results:
            return []

        # 评估每种方法的结果
        method_scores = []

        for method_name, char_images in methods_results:
            score = self._evaluate_segmentation_quality(char_images, original_image)
            method_scores.append((score, method_name, char_images))
            self.logger.debug(f"{method_name} 方法得分: {score:.3f}")

        # 选择得分最高的方法
        method_scores.sort(key=lambda x: x[0], reverse=True)
        best_score, best_method, best_chars = method_scores[0]

        self.logger.info(f"选择最佳分割方法: {best_method} (得分: {best_score:.3f})")

        return best_chars

    def _evaluate_segmentation_quality(self, char_images: List[np.ndarray],
                                     original_image: np.ndarray) -> float:
        """
        评估分割质量

        Args:
            char_images (List[np.ndarray]): 字符图像列表
            original_image (np.ndarray): 原始图像

        Returns:
            float: 质量得分
        """
        if not char_images:
            return 0.0

        score = 0.0

        # 1. 字符数量得分 (期望7-8个字符)
        num_chars = len(char_images)
        if 6 <= num_chars <= 8:
            char_count_score = 1.0
        elif 5 <= num_chars <= 9:
            char_count_score = 0.8
        else:
            char_count_score = max(0.0, 1.0 - abs(num_chars - 7) * 0.1)

        score += char_count_score * 0.3

        # 2. 字符尺寸一致性得分
        if num_chars > 1:
            widths = [img.shape[1] for img in char_images]
            heights = [img.shape[0] for img in char_images]

            width_std = np.std(widths) / np.mean(widths) if np.mean(widths) > 0 else 1.0
            height_std = np.std(heights) / np.mean(heights) if np.mean(heights) > 0 else 1.0

            consistency_score = max(0.0, 1.0 - (width_std + height_std))
            score += consistency_score * 0.3

        # 3. 字符清晰度得分
        clarity_scores = []
        for char_img in char_images:
            if len(char_img.shape) == 3:
                gray_char = cv2.cvtColor(char_img, cv2.COLOR_BGR2GRAY)
            else:
                gray_char = char_img

            # 使用拉普拉斯算子计算清晰度
            laplacian_var = cv2.Laplacian(gray_char, cv2.CV_64F).var()
            clarity_scores.append(laplacian_var)

        if clarity_scores:
            avg_clarity = np.mean(clarity_scores)
            clarity_score = min(1.0, avg_clarity / 1000.0)  # 归一化
            score += clarity_score * 0.4

        return score

    def _debug_adaptive_visualization(self, original_image: np.ndarray,
                                    processed_image: np.ndarray,
                                    methods_results: List[Tuple[str, List[np.ndarray]]]) -> None:
        """
        自适应分割调试可视化

        Args:
            original_image (np.ndarray): 原始图像
            processed_image (np.ndarray): 处理后图像
            methods_results (List[Tuple[str, List[np.ndarray]]]): 各方法结果
        """
        num_methods = len(methods_results)
        fig, axes = plt.subplots(2, max(2, num_methods), figsize=(5 * num_methods, 8))

        if num_methods == 1:
            axes = axes.reshape(2, 1)

        # 原始图像和预处理图像
        axes[0, 0].imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
        axes[0, 0].set_title('原始车牌图像')
        axes[0, 0].axis('off')

        axes[1, 0].imshow(processed_image, cmap='gray')
        axes[1, 0].set_title('预处理图像')
        axes[1, 0].axis('off')

        # 各方法的分割结果
        for i, (method_name, char_images) in enumerate(methods_results):
            if i + 1 < axes.shape[1]:
                # 显示分割后的字符
                if char_images:
                    # 将所有字符拼接显示
                    max_height = max(img.shape[0] for img in char_images)
                    padded_chars = []
                    for char_img in char_images:
                        if len(char_img.shape) == 3:
                            char_display = char_img
                        else:
                            char_display = cv2.cvtColor(char_img, cv2.COLOR_GRAY2RGB)

                        # 填充到相同高度
                        pad_height = max_height - char_display.shape[0]
                        if pad_height > 0:
                            char_display = cv2.copyMakeBorder(
                                char_display, pad_height//2, pad_height - pad_height//2,
                                0, 0, cv2.BORDER_CONSTANT, value=(255, 255, 255)
                            )
                        padded_chars.append(char_display)

                    # 水平拼接
                    combined = np.hstack(padded_chars)
                    axes[0, i + 1].imshow(combined)
                    axes[0, i + 1].set_title(f'{method_name} ({len(char_images)}个字符)')
                    axes[0, i + 1].axis('off')
                else:
                    axes[0, i + 1].text(0.5, 0.5, '无分割结果', ha='center', va='center')
                    axes[0, i + 1].set_title(f'{method_name} (0个字符)')
                    axes[0, i + 1].axis('off')

        plt.tight_layout()
        plt.show()
