#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合成车牌数据生成器
Synthetic License Plate Generator

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import cv2
import numpy as np
import random
import string
from pathlib import Path
from typing import Dict, Any, Tuple, List
import json

from ..utils.logger import LoggerMixin


class SyntheticPlateGenerator(LoggerMixin):
    """合成车牌数据生成器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化生成器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        
        # 车牌尺寸 (宽, 高)
        self.plate_size = (440, 140)
        
        # 字符集定义
        self.provinces = ['京', '津', '沪', '渝', '冀', '豫', '云', '辽', '黑', '湘', '皖', '鲁', 
                         '新', '苏', '浙', '赣', '鄂', '桂', '甘', '晋', '蒙', '陕', '吉', '闽', 
                         '贵', '粤', '青', '藏', '川', '宁', '琼']
        
        self.letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 
                       'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
        
        self.digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
        
        # 车牌颜色配置 (背景色, 文字色)
        self.plate_colors = {
            'blue': ((255, 255, 0), (0, 0, 0)),      # 蓝底黑字
            'yellow': ((0, 255, 255), (0, 0, 0)),    # 黄底黑字
            'green': ((0, 255, 0), (0, 0, 0)),       # 绿底黑字
            'white': ((255, 255, 255), (0, 0, 0)),   # 白底黑字
        }
        
        self.logger.info("合成车牌生成器初始化完成")
    
    def generate_plate_number(self, plate_type: str = 'normal') -> str:
        """
        生成车牌号码
        
        Args:
            plate_type (str): 车牌类型 ('normal', 'new_energy')
            
        Returns:
            str: 车牌号码
        """
        if plate_type == 'new_energy':
            # 新能源车牌 (8位)
            province = random.choice(self.provinces)
            city_code = random.choice(self.letters)
            
            # 新能源车牌的特殊规则
            if random.choice([True, False]):
                # D或F开头
                special_char = random.choice(['D', 'F'])
                remaining = ''.join(random.choices(self.digits + self.letters, k=5))
            else:
                # 数字开头
                first_digit = random.choice(self.digits)
                remaining = ''.join(random.choices(self.digits + self.letters, k=4))
                special_char = random.choice(['D', 'F'])
                remaining = first_digit + remaining + special_char
            
            return f"{province}{city_code}{remaining}"
        else:
            # 普通车牌 (7位)
            province = random.choice(self.provinces)
            city_code = random.choice(self.letters)
            number_part = ''.join(random.choices(self.digits + self.letters, k=5))
            
            return f"{province}{city_code}{number_part}"
    
    def create_plate_image(self, plate_number: str, plate_color: str = 'blue') -> Tuple[np.ndarray, Dict]:
        """
        创建车牌图像
        
        Args:
            plate_number (str): 车牌号码
            plate_color (str): 车牌颜色
            
        Returns:
            Tuple[np.ndarray, Dict]: 车牌图像和标注信息
        """
        # 获取颜色配置
        bg_color, text_color = self.plate_colors[plate_color]
        
        # 创建背景
        plate_img = np.full((self.plate_size[1], self.plate_size[0], 3), bg_color, dtype=np.uint8)
        
        # 绘制边框
        cv2.rectangle(plate_img, (5, 5), (self.plate_size[0]-5, self.plate_size[1]-5), (0, 0, 0), 2)
        
        # 字符位置和大小配置
        char_positions = self._calculate_char_positions(len(plate_number))
        
        # 标注信息
        annotations = {
            'plate_number': plate_number,
            'plate_color': plate_color,
            'characters': []
        }
        
        # 绘制字符
        for i, char in enumerate(plate_number):
            x, y = char_positions[i]
            
            # 选择字体大小
            font_scale = 2.0 if i == 0 else 1.8  # 省份字符稍大
            thickness = 3
            
            # 获取文字尺寸
            (text_width, text_height), baseline = cv2.getTextSize(
                char, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness
            )
            
            # 计算居中位置
            text_x = x - text_width // 2
            text_y = y + text_height // 2
            
            # 绘制字符
            cv2.putText(plate_img, char, (text_x, text_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, font_scale, text_color, thickness)
            
            # 记录字符位置信息
            char_bbox = {
                'character': char,
                'bbox': [text_x, text_y - text_height, text_x + text_width, text_y + baseline],
                'center': [x, y]
            }
            annotations['characters'].append(char_bbox)
        
        return plate_img, annotations

    def _calculate_char_positions(self, num_chars: int) -> List[Tuple[int, int]]:
        """
        计算字符位置

        Args:
            num_chars (int): 字符数量

        Returns:
            List[Tuple[int, int]]: 字符中心位置列表
        """
        positions = []

        if num_chars == 7:  # 普通车牌
            # 省份字符位置
            positions.append((50, 70))
            # 城市代码位置
            positions.append((110, 70))
            # 分隔符后的5个字符
            start_x = 180
            for i in range(5):
                positions.append((start_x + i * 50, 70))
        elif num_chars == 8:  # 新能源车牌
            # 省份字符位置
            positions.append((40, 70))
            # 城市代码位置
            positions.append((90, 70))
            # 后面6个字符
            start_x = 150
            for i in range(6):
                positions.append((start_x + i * 40, 70))

        return positions

    def apply_augmentations(self, image: np.ndarray) -> np.ndarray:
        """
        应用数据增强

        Args:
            image (np.ndarray): 输入图像

        Returns:
            np.ndarray: 增强后的图像
        """
        # 随机旋转
        if random.random() < 0.3:
            angle = random.uniform(-15, 15)
            center = (image.shape[1] // 2, image.shape[0] // 2)
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
            image = cv2.warpAffine(image, rotation_matrix, (image.shape[1], image.shape[0]))

        # 随机亮度调整
        if random.random() < 0.5:
            brightness = random.uniform(0.7, 1.3)
            image = cv2.convertScaleAbs(image, alpha=brightness, beta=0)

        # 随机对比度调整
        if random.random() < 0.5:
            contrast = random.uniform(0.8, 1.2)
            image = cv2.convertScaleAbs(image, alpha=contrast, beta=0)

        # 随机噪声
        if random.random() < 0.3:
            noise = np.random.normal(0, 10, image.shape).astype(np.uint8)
            image = cv2.add(image, noise)

        # 随机模糊
        if random.random() < 0.2:
            kernel_size = random.choice([3, 5])
            image = cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)

        return image

    def generate_dataset(self, output_dir: Path, num_samples: int) -> int:
        """
        生成合成数据集

        Args:
            output_dir (Path): 输出目录
            num_samples (int): 样本数量

        Returns:
            int: 成功生成的样本数量
        """
        output_dir.mkdir(parents=True, exist_ok=True)

        # 创建子目录
        images_dir = output_dir / 'images'
        annotations_dir = output_dir / 'annotations'
        images_dir.mkdir(exist_ok=True)
        annotations_dir.mkdir(exist_ok=True)

        success_count = 0

        self.logger.info(f"开始生成 {num_samples} 个合成车牌样本")

        for i in range(num_samples):
            try:
                # 随机选择车牌类型和颜色
                plate_type = random.choice(['normal', 'new_energy'])
                plate_color = random.choice(list(self.plate_colors.keys()))

                # 生成车牌号码
                plate_number = self.generate_plate_number(plate_type)

                # 创建车牌图像
                plate_img, annotations = self.create_plate_image(plate_number, plate_color)

                # 应用数据增强
                augmented_img = self.apply_augmentations(plate_img.copy())

                # 保存图像
                img_filename = f"synthetic_{i:06d}.jpg"
                img_path = images_dir / img_filename
                cv2.imwrite(str(img_path), augmented_img)

                # 保存标注
                annotations['image_filename'] = img_filename
                annotations['image_size'] = [augmented_img.shape[1], augmented_img.shape[0]]

                ann_filename = f"synthetic_{i:06d}.json"
                ann_path = annotations_dir / ann_filename

                with open(ann_path, 'w', encoding='utf-8') as f:
                    json.dump(annotations, f, ensure_ascii=False, indent=2)

                success_count += 1

                if (i + 1) % 100 == 0:
                    self.logger.info(f"已生成 {i + 1}/{num_samples} 个样本")

            except Exception as e:
                self.logger.error(f"生成第 {i} 个样本失败: {str(e)}")

        # 生成数据集统计信息
        self._generate_dataset_stats(output_dir, success_count)

        self.logger.info(f"合成数据集生成完成，成功生成 {success_count} 个样本")
        return success_count

    def _generate_dataset_stats(self, output_dir: Path, num_samples: int) -> None:
        """
        生成数据集统计信息

        Args:
            output_dir (Path): 输出目录
            num_samples (int): 样本数量
        """
        stats = {
            'dataset_type': 'synthetic_license_plates',
            'total_samples': num_samples,
            'plate_colors': list(self.plate_colors.keys()),
            'plate_types': ['normal', 'new_energy'],
            'image_size': self.plate_size,
            'character_classes': {
                'provinces': len(self.provinces),
                'letters': len(self.letters),
                'digits': len(self.digits),
                'total': len(self.provinces) + len(self.letters) + len(self.digits)
            },
            'generation_date': str(Path().cwd()),
            'description': '合成车牌数据集，用于车牌识别模型训练'
        }

        stats_path = output_dir / 'dataset_stats.json'
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
