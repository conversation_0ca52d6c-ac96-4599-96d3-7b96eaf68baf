#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合成车牌数据生成器
Synthetic License Plate Generator

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import cv2
import numpy as np
import random
import string
from pathlib import Path
from typing import Dict, Any, Tuple, List
import json

from ..utils.logger import LoggerMixin


class SyntheticPlateGenerator(LoggerMixin):
    """合成车牌数据生成器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化生成器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        
        # 车牌尺寸 (宽, 高)
        self.plate_size = (440, 140)
        
        # 字符集定义
        self.provinces = ['京', '津', '沪', '渝', '冀', '豫', '云', '辽', '黑', '湘', '皖', '鲁', 
                         '新', '苏', '浙', '赣', '鄂', '桂', '甘', '晋', '蒙', '陕', '吉', '闽', 
                         '贵', '粤', '青', '藏', '川', '宁', '琼']
        
        self.letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 
                       'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
        
        self.digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
        
        # 车牌颜色配置 (背景色, 文字色)
        self.plate_colors = {
            'blue': ((255, 255, 0), (0, 0, 0)),      # 蓝底黑字
            'yellow': ((0, 255, 255), (0, 0, 0)),    # 黄底黑字
            'green': ((0, 255, 0), (0, 0, 0)),       # 绿底黑字
            'white': ((255, 255, 255), (0, 0, 0)),   # 白底黑字
        }
        
        self.logger.info("合成车牌生成器初始化完成")
    
    def generate_plate_number(self, plate_type: str = 'normal') -> str:
        """
        生成车牌号码
        
        Args:
            plate_type (str): 车牌类型 ('normal', 'new_energy')
            
        Returns:
            str: 车牌号码
        """
        if plate_type == 'new_energy':
            # 新能源车牌 (8位)
            province = random.choice(self.provinces)
            city_code = random.choice(self.letters)
            
            # 新能源车牌的特殊规则
            if random.choice([True, False]):
                # D或F开头
                special_char = random.choice(['D', 'F'])
                remaining = ''.join(random.choices(self.digits + self.letters, k=5))
            else:
                # 数字开头
                first_digit = random.choice(self.digits)
                remaining = ''.join(random.choices(self.digits + self.letters, k=4))
                special_char = random.choice(['D', 'F'])
                remaining = first_digit + remaining + special_char
            
            return f"{province}{city_code}{remaining}"
        else:
            # 普通车牌 (7位)
            province = random.choice(self.provinces)
            city_code = random.choice(self.letters)
            number_part = ''.join(random.choices(self.digits + self.letters, k=5))
            
            return f"{province}{city_code}{number_part}"
    
    def create_plate_image(self, plate_number: str, plate_color: str = 'blue') -> Tuple[np.ndarray, Dict]:
        """
        创建车牌图像
        
        Args:
            plate_number (str): 车牌号码
            plate_color (str): 车牌颜色
            
        Returns:
            Tuple[np.ndarray, Dict]: 车牌图像和标注信息
        """
        # 获取颜色配置
        bg_color, text_color = self.plate_colors[plate_color]
        
        # 创建背景
        plate_img = np.full((self.plate_size[1], self.plate_size[0], 3), bg_color, dtype=np.uint8)
        
        # 绘制边框
        cv2.rectangle(plate_img, (5, 5), (self.plate_size[0]-5, self.plate_size[1]-5), (0, 0, 0), 2)
        
        # 字符位置和大小配置
        char_positions = self._calculate_char_positions(len(plate_number))
        
        # 标注信息
        annotations = {
            'plate_number': plate_number,
            'plate_color': plate_color,
            'characters': []
        }
        
        # 绘制字符
        for i, char in enumerate(plate_number):
            x, y = char_positions[i]
            
            # 选择字体大小
            font_scale = 2.0 if i == 0 else 1.8  # 省份字符稍大
            thickness = 3
            
            # 获取文字尺寸
            (text_width, text_height), baseline = cv2.getTextSize(
                char, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness
            )
            
            # 计算居中位置
            text_x = x - text_width // 2
            text_y = y + text_height // 2
            
            # 绘制字符
            cv2.putText(plate_img, char, (text_x, text_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, font_scale, text_color, thickness)
            
            # 记录字符位置信息
            char_bbox = {
                'character': char,
                'bbox': [text_x, text_y - text_height, text_x + text_width, text_y + baseline],
                'center': [x, y]
            }
            annotations['characters'].append(char_bbox)
        
        return plate_img, annotations

    def _calculate_char_positions(self, num_chars: int) -> List[Tuple[int, int]]:
        """
        计算字符位置

        Args:
            num_chars (int): 字符数量

        Returns:
            List[Tuple[int, int]]: 字符中心位置列表
        """
        positions = []

        if num_chars == 7:  # 普通车牌
            # 省份字符位置
            positions.append((50, 70))
            # 城市代码位置
            positions.append((110, 70))
            # 分隔符后的5个字符
            start_x = 180
            for i in range(5):
                positions.append((start_x + i * 50, 70))
        elif num_chars == 8:  # 新能源车牌
            # 省份字符位置
            positions.append((40, 70))
            # 城市代码位置
            positions.append((90, 70))
            # 后面6个字符
            start_x = 150
            for i in range(6):
                positions.append((start_x + i * 40, 70))

        return positions

    def apply_augmentations(self, image: np.ndarray) -> np.ndarray:
        """
        应用数据增强

        Args:
            image (np.ndarray): 输入图像

        Returns:
            np.ndarray: 增强后的图像
        """
        # 随机旋转
        if random.random() < 0.3:
            angle = random.uniform(-15, 15)
            center = (image.shape[1] // 2, image.shape[0] // 2)
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
            image = cv2.warpAffine(image, rotation_matrix, (image.shape[1], image.shape[0]))

        # 随机亮度调整
        if random.random() < 0.5:
            brightness = random.uniform(0.7, 1.3)
            image = cv2.convertScaleAbs(image, alpha=brightness, beta=0)

        # 随机对比度调整
        if random.random() < 0.5:
            contrast = random.uniform(0.8, 1.2)
            image = cv2.convertScaleAbs(image, alpha=contrast, beta=0)

        # 随机噪声
        if random.random() < 0.3:
            noise = np.random.normal(0, 10, image.shape).astype(np.uint8)
            image = cv2.add(image, noise)

        # 随机模糊
        if random.random() < 0.2:
            kernel_size = random.choice([3, 5])
            image = cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)

        # 随机透视变换
        if random.random() < 0.2:
            image = self._apply_perspective_transform(image)

        # 随机光照变化
        if random.random() < 0.3:
            image = self._apply_lighting_effects(image)

        # 随机污损效果
        if random.random() < 0.1:
            image = self._apply_dirt_effects(image)

        return image

    def _apply_perspective_transform(self, image: np.ndarray) -> np.ndarray:
        """
        应用透视变换

        Args:
            image (np.ndarray): 输入图像

        Returns:
            np.ndarray: 变换后的图像
        """
        h, w = image.shape[:2]

        # 定义源点和目标点
        src_points = np.float32([[0, 0], [w, 0], [w, h], [0, h]])

        # 随机偏移
        offset = random.uniform(5, 15)
        dst_points = np.float32([
            [random.uniform(0, offset), random.uniform(0, offset)],
            [w - random.uniform(0, offset), random.uniform(0, offset)],
            [w - random.uniform(0, offset), h - random.uniform(0, offset)],
            [random.uniform(0, offset), h - random.uniform(0, offset)]
        ])

        # 计算透视变换矩阵
        matrix = cv2.getPerspectiveTransform(src_points, dst_points)

        # 应用变换
        transformed = cv2.warpPerspective(image, matrix, (w, h))

        return transformed

    def _apply_lighting_effects(self, image: np.ndarray) -> np.ndarray:
        """
        应用光照效果

        Args:
            image (np.ndarray): 输入图像

        Returns:
            np.ndarray: 处理后的图像
        """
        h, w = image.shape[:2]

        # 创建渐变光照
        if random.choice([True, False]):
            # 水平渐变
            gradient = np.linspace(0.7, 1.3, w)
            gradient = np.tile(gradient, (h, 1))
        else:
            # 垂直渐变
            gradient = np.linspace(0.7, 1.3, h)
            gradient = np.tile(gradient.reshape(-1, 1), (1, w))

        # 应用渐变
        for c in range(3):
            image[:, :, c] = np.clip(image[:, :, c] * gradient, 0, 255)

        return image.astype(np.uint8)

    def _apply_dirt_effects(self, image: np.ndarray) -> np.ndarray:
        """
        应用污损效果

        Args:
            image (np.ndarray): 输入图像

        Returns:
            np.ndarray: 处理后的图像
        """
        h, w = image.shape[:2]

        # 随机添加污点
        num_spots = random.randint(1, 5)
        for _ in range(num_spots):
            # 随机位置和大小
            x = random.randint(0, w - 20)
            y = random.randint(0, h - 20)
            radius = random.randint(3, 10)

            # 随机颜色（偏暗）
            color = (
                random.randint(0, 100),
                random.randint(0, 100),
                random.randint(0, 100)
            )

            # 绘制污点
            cv2.circle(image, (x, y), radius, color, -1)

            # 添加模糊效果
            mask = np.zeros((h, w), dtype=np.uint8)
            cv2.circle(mask, (x, y), radius, 255, -1)
            image = cv2.GaussianBlur(image, (5, 5), 0, dst=image, borderType=cv2.BORDER_DEFAULT)

        return image

    def create_realistic_scene(self, plate_image: np.ndarray, scene_type: str = 'car') -> np.ndarray:
        """
        创建真实场景中的车牌图像

        Args:
            plate_image (np.ndarray): 车牌图像
            scene_type (str): 场景类型 ('car', 'parking', 'street')

        Returns:
            np.ndarray: 场景图像
        """
        # 创建背景
        if scene_type == 'car':
            background = self._create_car_background()
        elif scene_type == 'parking':
            background = self._create_parking_background()
        else:
            background = self._create_street_background()

        # 将车牌嵌入背景
        scene_image = self._embed_plate_in_scene(background, plate_image)

        return scene_image

    def _create_car_background(self) -> np.ndarray:
        """
        创建汽车背景

        Returns:
            np.ndarray: 背景图像
        """
        # 创建简单的汽车后部背景
        bg_width, bg_height = 800, 600
        background = np.random.randint(80, 120, (bg_height, bg_width, 3), dtype=np.uint8)

        # 添加汽车轮廓
        car_color = (random.randint(50, 200), random.randint(50, 200), random.randint(50, 200))
        cv2.rectangle(background, (100, 200), (700, 500), car_color, -1)

        # 添加一些细节
        # 后窗
        cv2.rectangle(background, (150, 220), (650, 300), (20, 20, 40), -1)

        # 尾灯
        cv2.rectangle(background, (120, 350), (180, 420), (150, 20, 20), -1)
        cv2.rectangle(background, (620, 350), (680, 420), (150, 20, 20), -1)

        return background

    def _create_parking_background(self) -> np.ndarray:
        """
        创建停车场背景

        Returns:
            np.ndarray: 背景图像
        """
        bg_width, bg_height = 800, 600

        # 地面
        ground_color = (random.randint(80, 120), random.randint(80, 120), random.randint(80, 120))
        background = np.full((bg_height, bg_width, 3), ground_color, dtype=np.uint8)

        # 停车线
        line_color = (255, 255, 255)
        for i in range(0, bg_width, 100):
            cv2.line(background, (i, bg_height//2), (i, bg_height), line_color, 2)

        # 添加其他车辆轮廓
        for _ in range(random.randint(1, 3)):
            x = random.randint(50, bg_width - 200)
            y = random.randint(bg_height//2, bg_height - 100)
            w = random.randint(150, 200)
            h = random.randint(80, 120)

            car_color = (random.randint(50, 200), random.randint(50, 200), random.randint(50, 200))
            cv2.rectangle(background, (x, y), (x + w, y + h), car_color, -1)

        return background

    def _create_street_background(self) -> np.ndarray:
        """
        创建街道背景

        Returns:
            np.ndarray: 背景图像
        """
        bg_width, bg_height = 800, 600

        # 天空
        sky_color = (random.randint(180, 255), random.randint(200, 255), random.randint(220, 255))
        background = np.full((bg_height//2, bg_width, 3), sky_color, dtype=np.uint8)

        # 地面
        ground_color = (random.randint(60, 100), random.randint(60, 100), random.randint(60, 100))
        ground = np.full((bg_height//2, bg_width, 3), ground_color, dtype=np.uint8)

        background = np.vstack([background, ground])

        # 添加建筑物轮廓
        for _ in range(random.randint(2, 4)):
            x = random.randint(0, bg_width - 100)
            height = random.randint(100, 200)
            width = random.randint(80, 150)

            building_color = (random.randint(100, 180), random.randint(100, 180), random.randint(100, 180))
            cv2.rectangle(background, (x, bg_height//2 - height), (x + width, bg_height//2), building_color, -1)

        return background

    def _embed_plate_in_scene(self, background: np.ndarray, plate_image: np.ndarray) -> np.ndarray:
        """
        将车牌嵌入场景

        Args:
            background (np.ndarray): 背景图像
            plate_image (np.ndarray): 车牌图像

        Returns:
            np.ndarray: 合成图像
        """
        bg_h, bg_w = background.shape[:2]
        plate_h, plate_w = plate_image.shape[:2]

        # 随机缩放车牌
        scale = random.uniform(0.3, 0.8)
        new_w = int(plate_w * scale)
        new_h = int(plate_h * scale)

        plate_resized = cv2.resize(plate_image, (new_w, new_h))

        # 随机位置（偏向中下部）
        x = random.randint(0, max(1, bg_w - new_w))
        y = random.randint(bg_h//2, max(bg_h//2 + 1, bg_h - new_h))

        # 创建合成图像
        result = background.copy()

        # 简单的alpha混合
        alpha = 0.9
        result[y:y+new_h, x:x+new_w] = (
            alpha * plate_resized + (1 - alpha) * result[y:y+new_h, x:x+new_w]
        ).astype(np.uint8)

        return result

    def generate_dataset(self, output_dir: Path, num_samples: int, include_scenes: bool = True) -> int:
        """
        生成合成数据集

        Args:
            output_dir (Path): 输出目录
            num_samples (int): 样本数量
            include_scenes (bool): 是否包含场景图像

        Returns:
            int: 成功生成的样本数量
        """
        output_dir.mkdir(parents=True, exist_ok=True)

        # 创建子目录
        images_dir = output_dir / 'images'
        annotations_dir = output_dir / 'annotations'
        plates_only_dir = output_dir / 'plates_only'  # 仅车牌图像
        scenes_dir = output_dir / 'scenes'  # 场景图像

        images_dir.mkdir(exist_ok=True)
        annotations_dir.mkdir(exist_ok=True)
        plates_only_dir.mkdir(exist_ok=True)
        if include_scenes:
            scenes_dir.mkdir(exist_ok=True)

        success_count = 0

        self.logger.info(f"开始生成 {num_samples} 个合成车牌样本")

        for i in range(num_samples):
            try:
                # 随机选择车牌类型和颜色
                plate_type = random.choice(['normal', 'new_energy'])
                plate_color = random.choice(list(self.plate_colors.keys()))

                # 生成车牌号码
                plate_number = self.generate_plate_number(plate_type)

                # 创建车牌图像
                plate_img, annotations = self.create_plate_image(plate_number, plate_color)

                # 应用数据增强
                augmented_img = self.apply_augmentations(plate_img.copy())

                # 保存纯车牌图像
                plate_filename = f"plate_{i:06d}.jpg"
                plate_path = plates_only_dir / plate_filename
                cv2.imwrite(str(plate_path), augmented_img)

                # 生成场景图像（如果需要）
                final_img = augmented_img
                if include_scenes and random.random() < 0.7:  # 70%的概率生成场景
                    scene_type = random.choice(['car', 'parking', 'street'])
                    scene_img = self.create_realistic_scene(augmented_img, scene_type)

                    # 保存场景图像
                    scene_filename = f"scene_{i:06d}.jpg"
                    scene_path = scenes_dir / scene_filename
                    cv2.imwrite(str(scene_path), scene_img)

                    final_img = scene_img
                    annotations['scene_type'] = scene_type
                    annotations['has_scene'] = True
                else:
                    annotations['has_scene'] = False

                # 保存最终图像
                img_filename = f"synthetic_{i:06d}.jpg"
                img_path = images_dir / img_filename
                cv2.imwrite(str(img_path), final_img)

                # 更新标注信息
                annotations['image_filename'] = img_filename
                annotations['plate_filename'] = plate_filename
                annotations['image_size'] = [final_img.shape[1], final_img.shape[0]]
                annotations['generation_params'] = {
                    'plate_type': plate_type,
                    'plate_color': plate_color,
                    'augmented': True,
                    'include_scenes': include_scenes
                }

                # 保存标注
                ann_filename = f"synthetic_{i:06d}.json"
                ann_path = annotations_dir / ann_filename

                with open(ann_path, 'w', encoding='utf-8') as f:
                    json.dump(annotations, f, ensure_ascii=False, indent=2)

                success_count += 1

                if (i + 1) % 100 == 0:
                    self.logger.info(f"已生成 {i + 1}/{num_samples} 个样本")

            except Exception as e:
                self.logger.error(f"生成第 {i} 个样本失败: {str(e)}")

        # 生成数据集统计信息
        self._generate_dataset_stats(output_dir, success_count, include_scenes)

        self.logger.info(f"合成数据集生成完成，成功生成 {success_count} 个样本")
        return success_count

    def _generate_dataset_stats(self, output_dir: Path, num_samples: int, include_scenes: bool = False) -> None:
        """
        生成数据集统计信息

        Args:
            output_dir (Path): 输出目录
            num_samples (int): 样本数量
            include_scenes (bool): 是否包含场景图像
        """
        from datetime import datetime

        stats = {
            'dataset_type': 'synthetic_license_plates',
            'version': '2.0',
            'total_samples': num_samples,
            'plate_colors': list(self.plate_colors.keys()),
            'plate_types': ['normal', 'new_energy'],
            'base_plate_size': self.plate_size,
            'character_classes': {
                'provinces': len(self.provinces),
                'letters': len(self.letters),
                'digits': len(self.digits),
                'total': len(self.provinces) + len(self.letters) + len(self.digits)
            },
            'augmentation_features': [
                'rotation', 'brightness', 'contrast', 'noise', 'blur',
                'perspective_transform', 'lighting_effects', 'dirt_effects'
            ],
            'scene_types': ['car', 'parking', 'street'] if include_scenes else [],
            'include_scenes': include_scenes,
            'generation_date': datetime.now().isoformat(),
            'directory_structure': {
                'images': '主要图像目录',
                'annotations': '标注文件目录',
                'plates_only': '纯车牌图像目录',
                'scenes': '场景图像目录（如果启用）'
            },
            'description': '增强版合成车牌数据集，支持多种数据增强和真实场景生成'
        }

        stats_path = output_dir / 'dataset_stats.json'
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)

        # 生成README文件
        readme_content = f"""# 合成车牌数据集

## 数据集信息
- **类型**: {stats['dataset_type']}
- **版本**: {stats['version']}
- **样本数量**: {stats['total_samples']}
- **生成日期**: {stats['generation_date']}

## 车牌类型
- 普通车牌 (7位): 省份 + 城市代码 + 5位字符
- 新能源车牌 (8位): 省份 + 城市代码 + 6位字符

## 支持的车牌颜色
{', '.join(stats['plate_colors'])}

## 字符类别统计
- 省份字符: {stats['character_classes']['provinces']} 个
- 字母: {stats['character_classes']['letters']} 个
- 数字: {stats['character_classes']['digits']} 个
- 总计: {stats['character_classes']['total']} 个

## 数据增强特性
{chr(10).join('- ' + feature for feature in stats['augmentation_features'])}

## 目录结构
- `images/`: 主要训练图像
- `annotations/`: JSON格式标注文件
- `plates_only/`: 纯车牌图像（无背景）
{'- `scenes/`: 场景图像（包含背景）' if include_scenes else ''}

## 使用说明
1. 图像文件格式: JPEG
2. 标注文件格式: JSON
3. 每个图像都有对应的标注文件
4. 标注包含车牌号码、字符位置、颜色等信息

## 注意事项
- 本数据集为合成数据，用于模型训练和测试
- 实际应用时建议结合真实数据进行训练
- 字符位置信息可用于字符分割和识别任务
"""

        readme_path = output_dir / 'README.md'
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
