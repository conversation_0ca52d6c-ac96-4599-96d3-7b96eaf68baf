#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础模型类
Base Model Class

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import torch
import torch.nn as nn
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import json

from ..utils.logger import LoggerMixin


class BaseModel(nn.Module, LoggerMixin, ABC):
    """基础模型抽象类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化基础模型
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        super(BaseModel, self).__init__()
        self.config = config
        self.model_config = config.get('model', {})
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 模型信息
        self.model_name = self.__class__.__name__
        self.version = "1.0.0"
        self.input_size = None
        self.num_classes = None
        
        self.logger.info(f"初始化模型: {self.model_name}")
        self.logger.info(f"使用设备: {self.device}")
    
    @abstractmethod
    def build_model(self) -> None:
        """构建模型架构"""
        pass
    
    @abstractmethod
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        pass
    
    def save_model(self, save_path: str, epoch: int = 0, 
                   optimizer_state: Optional[Dict] = None,
                   metrics: Optional[Dict] = None) -> None:
        """
        保存模型
        
        Args:
            save_path (str): 保存路径
            epoch (int): 训练轮数
            optimizer_state (Optional[Dict]): 优化器状态
            metrics (Optional[Dict]): 评估指标
        """
        save_path = Path(save_path)
        save_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 准备保存的数据
        save_data = {
            'model_name': self.model_name,
            'version': self.version,
            'epoch': epoch,
            'model_state_dict': self.state_dict(),
            'config': self.config,
            'input_size': self.input_size,
            'num_classes': self.num_classes,
            'device': str(self.device)
        }
        
        if optimizer_state is not None:
            save_data['optimizer_state_dict'] = optimizer_state
        
        if metrics is not None:
            save_data['metrics'] = metrics
        
        # 保存模型
        torch.save(save_data, save_path)
        
        # 保存模型信息到JSON文件
        info_path = save_path.with_suffix('.json')
        model_info = {
            'model_name': self.model_name,
            'version': self.version,
            'epoch': epoch,
            'input_size': self.input_size,
            'num_classes': self.num_classes,
            'parameters': self.count_parameters(),
            'device': str(self.device),
            'metrics': metrics or {}
        }
        
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(model_info, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"模型已保存: {save_path}")
    
    def load_model(self, load_path: str, load_optimizer: bool = False) -> Optional[Dict]:
        """
        加载模型
        
        Args:
            load_path (str): 模型路径
            load_optimizer (bool): 是否加载优化器状态
            
        Returns:
            Optional[Dict]: 优化器状态（如果加载）
        """
        load_path = Path(load_path)
        
        if not load_path.exists():
            self.logger.error(f"模型文件不存在: {load_path}")
            return None
        
        try:
            # 加载模型数据
            checkpoint = torch.load(load_path, map_location=self.device)
            
            # 验证模型兼容性
            if checkpoint.get('model_name') != self.model_name:
                self.logger.warning(f"模型名称不匹配: {checkpoint.get('model_name')} vs {self.model_name}")
            
            # 加载模型状态
            self.load_state_dict(checkpoint['model_state_dict'])
            
            # 更新模型信息
            self.input_size = checkpoint.get('input_size', self.input_size)
            self.num_classes = checkpoint.get('num_classes', self.num_classes)
            
            self.logger.info(f"模型已加载: {load_path}")
            self.logger.info(f"训练轮数: {checkpoint.get('epoch', 0)}")
            
            # 返回优化器状态
            if load_optimizer and 'optimizer_state_dict' in checkpoint:
                return checkpoint['optimizer_state_dict']
            
            return None
            
        except Exception as e:
            self.logger.error(f"加载模型失败: {str(e)}")
            return None
    
    def count_parameters(self) -> int:
        """
        统计模型参数数量
        
        Returns:
            int: 参数数量
        """
        return sum(p.numel() for p in self.parameters() if p.requires_grad)
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            Dict[str, Any]: 模型信息
        """
        return {
            'model_name': self.model_name,
            'version': self.version,
            'parameters': self.count_parameters(),
            'input_size': self.input_size,
            'num_classes': self.num_classes,
            'device': str(self.device)
        }
    
    def to_device(self, device: Optional[torch.device] = None) -> 'BaseModel':
        """
        将模型移动到指定设备
        
        Args:
            device (Optional[torch.device]): 目标设备
            
        Returns:
            BaseModel: 模型实例
        """
        if device is not None:
            self.device = device
        
        self.to(self.device)
        self.logger.info(f"模型已移动到设备: {self.device}")
        return self
    
    def set_training_mode(self, training: bool = True) -> None:
        """
        设置训练模式
        
        Args:
            training (bool): 是否为训练模式
        """
        self.train(training)
        mode = "训练" if training else "评估"
        self.logger.info(f"模型设置为{mode}模式")
    
    def freeze_layers(self, layer_names: list) -> None:
        """
        冻结指定层的参数
        
        Args:
            layer_names (list): 要冻结的层名称列表
        """
        frozen_count = 0
        for name, param in self.named_parameters():
            if any(layer_name in name for layer_name in layer_names):
                param.requires_grad = False
                frozen_count += 1
        
        self.logger.info(f"已冻结 {frozen_count} 个参数")
    
    def unfreeze_layers(self, layer_names: list) -> None:
        """
        解冻指定层的参数
        
        Args:
            layer_names (list): 要解冻的层名称列表
        """
        unfrozen_count = 0
        for name, param in self.named_parameters():
            if any(layer_name in name for layer_name in layer_names):
                param.requires_grad = True
                unfrozen_count += 1
        
        self.logger.info(f"已解冻 {unfrozen_count} 个参数")
    
    def get_layer_names(self) -> list:
        """
        获取所有层的名称
        
        Returns:
            list: 层名称列表
        """
        return [name for name, _ in self.named_modules()]
    
    def summary(self) -> str:
        """
        获取模型摘要信息
        
        Returns:
            str: 模型摘要
        """
        summary_lines = [
            f"模型名称: {self.model_name}",
            f"版本: {self.version}",
            f"参数数量: {self.count_parameters():,}",
            f"输入尺寸: {self.input_size}",
            f"类别数量: {self.num_classes}",
            f"设备: {self.device}",
            "=" * 50
        ]
        
        # 添加层信息
        for name, module in self.named_modules():
            if len(list(module.children())) == 0:  # 叶子节点
                summary_lines.append(f"{name}: {module}")
        
        return "\n".join(summary_lines)
