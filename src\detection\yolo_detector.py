#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于YOLO的车牌检测器
YOLO-based License Plate Detector

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import cv2
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
import torchvision.transforms as transforms
import torchvision.ops as ops

from .plate_detector import PlateDetector
from ..utils.logger import LoggerMixin


class YOLODetector(PlateDetector):
    """基于YOLO的车牌检测器"""
    
    def __init__(self, config: Dict[str, Any], version: str = 'v5'):
        """
        初始化YOLO检测器
        
        Args:
            config (Dict[str, Any]): 配置字典
            version (str): YOLO版本 ('v5', 'v8')
        """
        super().__init__(config)
        self.version = version
        self.model_name = self.detection_config.get('model_name', f'yolo_{version}')
        
        # 尝试导入YOLO
        self.yolo_available = False
        try:
            if version == 'v5':
                import yolov5
                self.yolo_available = True
                self.logger.info("成功导入YOLOv5")
            elif version == 'v8':
                from ultralytics import YOLO
                self.yolo_available = True
                self.logger.info("成功导入YOLOv8")
            else:
                self.logger.warning(f"不支持的YOLO版本: {version}")
        except ImportError as e:
            self.logger.warning(f"无法导入YOLO: {str(e)}")
            self.logger.warning("将使用备用检测方法")
        
        self.logger.info(f"初始化YOLO{version}检测器")
    
    def load_model(self, model_path: str = None) -> bool:
        """
        加载YOLO模型
        
        Args:
            model_path (str): 模型文件路径，如果为None则使用预训练模型
            
        Returns:
            bool: 是否加载成功
        """
        if not self.yolo_available:
            self.logger.error("YOLO不可用，无法加载模型")
            return False
        
        try:
            if self.version == 'v5':
                # 加载YOLOv5模型
                if model_path and Path(model_path).exists():
                    self.model = torch.hub.load('ultralytics/yolov5', 'custom', path=model_path)
                else:
                    # 使用预训练模型
                    self.model = torch.hub.load('ultralytics/yolov5', 'yolov5s')
                
                # 设置参数
                self.model.conf = self.confidence_threshold
                self.model.iou = self.nms_threshold
                self.model.classes = [2]  # 只检测车辆类别
                self.model.to(self.device)
                
            elif self.version == 'v8':
                # 加载YOLOv8模型
                from ultralytics import YOLO
                if model_path and Path(model_path).exists():
                    self.model = YOLO(model_path)
                else:
                    # 使用预训练模型
                    self.model = YOLO('yolov8s.pt')
            
            self.logger.info(f"成功加载YOLO{self.version}模型")
            return True
            
        except Exception as e:
            self.logger.error(f"加载YOLO模型失败: {str(e)}")
            return False
    
    def detect(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        使用YOLO检测车牌
        
        Args:
            image (np.ndarray): 输入图像
            
        Returns:
            List[Dict[str, Any]]: 检测结果列表
        """
        if not self.yolo_available or self.model is None:
            self.logger.warning("YOLO模型不可用，使用备用方法")
            return super().detect(image)
        
        try:
            # 使用YOLO进行检测
            if self.version == 'v5':
                results = self.model(image)
                
                # 提取检测结果
                detections = []
                for pred in results.xyxy[0]:  # 只处理第一张图像的结果
                    x1, y1, x2, y2, conf, cls = pred.cpu().numpy()
                    
                    # 只保留车辆或车牌类别
                    if cls in [2, 7]:  # 2: car, 7: truck (根据COCO数据集)
                        detection = {
                            'bbox': [float(x1), float(y1), float(x2), float(y2)],
                            'confidence': float(conf),
                            'class': 'license_plate' if cls == 7 else 'vehicle'
                        }
                        detections.append(detection)
                
                # 如果检测到车辆但没有检测到车牌，尝试在车辆区域内查找车牌
                if any(d['class'] == 'vehicle' for d in detections) and not any(d['class'] == 'license_plate' for d in detections):
                    vehicle_detections = [d for d in detections if d['class'] == 'vehicle']
                    for vehicle in vehicle_detections:
                        x1, y1, x2, y2 = map(int, vehicle['bbox'])
                        vehicle_img = image[y1:y2, x1:x2]
                        if vehicle_img.size > 0:
                            # 在车辆区域内查找车牌
                            plate_detections = self._detect_plate_in_vehicle(vehicle_img)
                            
                            # 调整坐标到原始图像
                            for plate in plate_detections:
                                plate_bbox = plate['bbox']
                                plate_bbox[0] += x1
                                plate_bbox[1] += y1
                                plate_bbox[2] += x1
                                plate_bbox[3] += y1
                                detections.append(plate)
                
                return detections
                
            elif self.version == 'v8':
                results = self.model(image, conf=self.confidence_threshold)
                
                # 提取检测结果
                detections = []
                for r in results:
                    boxes = r.boxes
                    for box in boxes:
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        conf = box.conf[0].cpu().numpy()
                        cls = box.cls[0].cpu().numpy()
                        
                        # 只保留车辆或车牌类别
                        if cls in [2, 7]:  # 2: car, 7: truck (根据COCO数据集)
                            detection = {
                                'bbox': [float(x1), float(y1), float(x2), float(y2)],
                                'confidence': float(conf),
                                'class': 'license_plate' if cls == 7 else 'vehicle'
                            }
                            detections.append(detection)
                
                return detections
            
        except Exception as e:
            self.logger.error(f"YOLO检测失败: {str(e)}")
            return super().detect(image)  # 使用备用方法
    
    def _detect_plate_in_vehicle(self, vehicle_image: np.ndarray) -> List[Dict[str, Any]]:
        """
        在车辆区域内检测车牌
        
        Args:
            vehicle_image (np.ndarray): 车辆区域图像
            
        Returns:
            List[Dict[str, Any]]: 车牌检测结果
        """
        # 使用传统方法在车辆区域内查找车牌
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(vehicle_image, cv2.COLOR_BGR2GRAY)
            
            # 边缘检测
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)
            
            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            detections = []
            
            for contour in contours:
                # 计算轮廓的边界矩形
                x, y, w, h = cv2.boundingRect(contour)
                
                # 过滤条件：车牌的宽高比通常在2-5之间
                aspect_ratio = w / h if h > 0 else 0
                area = w * h
                
                if (2.0 <= aspect_ratio <= 5.0 and 
                    area > 1000 and 
                    w > 50 and h > 20):
                    
                    detection = {
                        'bbox': [x, y, x + w, y + h],
                        'confidence': min(0.8, area / 10000),  # 简单的置信度计算
                        'class': 'license_plate'
                    }
                    detections.append(detection)
            
            # 按置信度排序，返回最好的几个结果
            detections.sort(key=lambda x: x['confidence'], reverse=True)
            return detections[:2]  # 最多返回2个检测结果
            
        except Exception as e:
            self.logger.error(f"车辆内车牌检测失败: {str(e)}")
            return []


def create_yolo_detector(config: Dict[str, Any], version: str = 'v5') -> YOLODetector:
    """
    创建YOLO车牌检测器
    
    Args:
        config (Dict[str, Any]): 配置字典
        version (str): YOLO版本
        
    Returns:
        YOLODetector: 检测器实例
    """
    return YOLODetector(config, version)
