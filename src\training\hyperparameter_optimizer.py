#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超参数优化器
Hyperparameter Optimizer

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from typing import Dict, Any, List, Tuple, Optional, Callable
import itertools
import json
from pathlib import Path
import time
from dataclasses import dataclass
import optuna
from optuna.pruners import MedianPruner
from optuna.samplers import TPESampler

from ..utils.logger import LoggerMixin
from ..models.model_trainer import ModelTrainer
from ..models.character_recognizer import CharacterRecognizer, CharacterRecognizerCRNN, CharacterRecognizerTransformer


@dataclass
class HyperparameterConfig:
    """超参数配置"""
    learning_rate: float
    batch_size: int
    weight_decay: float
    optimizer: str
    scheduler: str
    dropout_rate: float
    hidden_size: int
    num_layers: int


class HyperparameterOptimizer(LoggerMixin):
    """超参数优化器"""
    
    def __init__(self, config: Dict[str, Any], 
                 train_dataset, val_dataset,
                 model_type: str = 'cnn'):
        """
        初始化超参数优化器
        
        Args:
            config (Dict[str, Any]): 基础配置
            train_dataset: 训练数据集
            val_dataset: 验证数据集
            model_type (str): 模型类型
        """
        self.config = config
        self.train_dataset = train_dataset
        self.val_dataset = val_dataset
        self.model_type = model_type
        
        # 设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 字符类别数
        self.num_classes = len(config.get('char_mapping', {}))
        
        # 优化历史
        self.optimization_history = []
        
        self.logger.info(f"超参数优化器初始化完成，模型类型: {model_type}")
    
    def grid_search(self, param_grid: Dict[str, List], 
                   max_trials: int = 50,
                   save_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        网格搜索优化
        
        Args:
            param_grid (Dict[str, List]): 参数网格
            max_trials (int): 最大试验次数
            save_dir (Optional[str]): 保存目录
            
        Returns:
            Dict[str, Any]: 最佳参数和结果
        """
        self.logger.info("开始网格搜索超参数优化...")
        
        # 生成所有参数组合
        param_names = list(param_grid.keys())
        param_values = list(param_grid.values())
        param_combinations = list(itertools.product(*param_values))
        
        # 限制试验次数
        if len(param_combinations) > max_trials:
            param_combinations = param_combinations[:max_trials]
            self.logger.warning(f"参数组合数量超过最大试验次数，只测试前 {max_trials} 个组合")
        
        best_score = 0.0
        best_params = None
        results = []
        
        for i, param_values in enumerate(param_combinations):
            self.logger.info(f"试验 {i+1}/{len(param_combinations)}")
            
            # 创建参数字典
            params = dict(zip(param_names, param_values))
            
            # 训练和评估
            score = self._evaluate_hyperparameters(params)
            
            # 记录结果
            result = {
                'trial': i + 1,
                'params': params,
                'score': score,
                'timestamp': time.time()
            }
            results.append(result)
            self.optimization_history.append(result)
            
            # 更新最佳结果
            if score > best_score:
                best_score = score
                best_params = params.copy()
                self.logger.info(f"发现更好的参数组合，分数: {score:.4f}")
        
        # 保存结果
        if save_dir:
            self._save_optimization_results(results, save_dir, 'grid_search')
        
        best_result = {
            'best_params': best_params,
            'best_score': best_score,
            'all_results': results
        }
        
        self.logger.info(f"网格搜索完成，最佳分数: {best_score:.4f}")
        return best_result
    
    def random_search(self, param_distributions: Dict[str, Callable],
                     n_trials: int = 50,
                     save_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        随机搜索优化
        
        Args:
            param_distributions (Dict[str, Callable]): 参数分布函数
            n_trials (int): 试验次数
            save_dir (Optional[str]): 保存目录
            
        Returns:
            Dict[str, Any]: 最佳参数和结果
        """
        self.logger.info("开始随机搜索超参数优化...")
        
        best_score = 0.0
        best_params = None
        results = []
        
        for i in range(n_trials):
            self.logger.info(f"试验 {i+1}/{n_trials}")
            
            # 随机采样参数
            params = {}
            for param_name, distribution_func in param_distributions.items():
                params[param_name] = distribution_func()
            
            # 训练和评估
            score = self._evaluate_hyperparameters(params)
            
            # 记录结果
            result = {
                'trial': i + 1,
                'params': params,
                'score': score,
                'timestamp': time.time()
            }
            results.append(result)
            self.optimization_history.append(result)
            
            # 更新最佳结果
            if score > best_score:
                best_score = score
                best_params = params.copy()
                self.logger.info(f"发现更好的参数组合，分数: {score:.4f}")
        
        # 保存结果
        if save_dir:
            self._save_optimization_results(results, save_dir, 'random_search')
        
        best_result = {
            'best_params': best_params,
            'best_score': best_score,
            'all_results': results
        }
        
        self.logger.info(f"随机搜索完成，最佳分数: {best_score:.4f}")
        return best_result
    
    def bayesian_optimization(self, n_trials: int = 100,
                            save_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        贝叶斯优化（使用Optuna）
        
        Args:
            n_trials (int): 试验次数
            save_dir (Optional[str]): 保存目录
            
        Returns:
            Dict[str, Any]: 最佳参数和结果
        """
        self.logger.info("开始贝叶斯优化超参数...")
        
        # 创建Optuna研究
        study = optuna.create_study(
            direction='maximize',
            sampler=TPESampler(seed=42),
            pruner=MedianPruner(n_startup_trials=5, n_warmup_steps=10)
        )
        
        # 定义目标函数
        def objective(trial):
            # 建议超参数
            params = self._suggest_hyperparameters(trial)
            
            # 评估参数
            score = self._evaluate_hyperparameters(params)
            
            # 记录结果
            result = {
                'trial': trial.number + 1,
                'params': params,
                'score': score,
                'timestamp': time.time()
            }
            self.optimization_history.append(result)
            
            return score
        
        # 运行优化
        study.optimize(objective, n_trials=n_trials)
        
        # 获取最佳结果
        best_params = study.best_params
        best_score = study.best_value
        
        # 保存结果
        if save_dir:
            results = [
                {
                    'trial': trial.number + 1,
                    'params': trial.params,
                    'score': trial.value,
                    'state': trial.state.name
                }
                for trial in study.trials
            ]
            self._save_optimization_results(results, save_dir, 'bayesian_optimization')
            
            # 保存Optuna研究
            study_path = Path(save_dir) / 'optuna_study.pkl'
            with open(study_path, 'wb') as f:
                import pickle
                pickle.dump(study, f)
        
        best_result = {
            'best_params': best_params,
            'best_score': best_score,
            'study': study
        }
        
        self.logger.info(f"贝叶斯优化完成，最佳分数: {best_score:.4f}")
        return best_result
    
    def _suggest_hyperparameters(self, trial) -> Dict[str, Any]:
        """
        建议超参数（用于Optuna）
        
        Args:
            trial: Optuna试验对象
            
        Returns:
            Dict[str, Any]: 建议的超参数
        """
        params = {
            'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-2, log=True),
            'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64, 128]),
            'weight_decay': trial.suggest_float('weight_decay', 1e-6, 1e-2, log=True),
            'optimizer': trial.suggest_categorical('optimizer', ['adam', 'adamw', 'sgd']),
            'scheduler': trial.suggest_categorical('scheduler', ['steplr', 'cosineannealinglr', 'reducelronplateau']),
            'dropout_rate': trial.suggest_float('dropout_rate', 0.1, 0.5)
        }
        
        # 模型特定参数
        if self.model_type in ['crnn', 'transformer']:
            params['hidden_size'] = trial.suggest_categorical('hidden_size', [128, 256, 512])
            params['num_layers'] = trial.suggest_int('num_layers', 1, 4)
        
        return params
    
    def _evaluate_hyperparameters(self, params: Dict[str, Any]) -> float:
        """
        评估超参数组合
        
        Args:
            params (Dict[str, Any]): 超参数
            
        Returns:
            float: 评估分数（验证准确率）
        """
        try:
            # 创建模型
            model = self._create_model_with_params(params)
            
            # 创建数据加载器
            train_loader = DataLoader(
                self.train_dataset,
                batch_size=params.get('batch_size', 32),
                shuffle=True,
                num_workers=2,
                pin_memory=True
            )
            
            val_loader = DataLoader(
                self.val_dataset,
                batch_size=params.get('batch_size', 32),
                shuffle=False,
                num_workers=2,
                pin_memory=True
            )
            
            # 创建训练器
            temp_config = self.config.copy()
            temp_config['training'] = {
                'epochs': 10,  # 快速评估，使用较少epoch
                'learning_rate': params.get('learning_rate', 0.001),
                'batch_size': params.get('batch_size', 32),
                'weight_decay': params.get('weight_decay', 1e-4)
            }
            
            trainer = ModelTrainer(model, temp_config)
            trainer.setup_training(
                optimizer_type=params.get('optimizer', 'adam'),
                criterion_type='crossentropy',
                scheduler_type=params.get('scheduler', 'steplr')
            )
            
            # 训练模型
            history = trainer.train(train_loader, val_loader)
            
            # 返回最佳验证准确率
            best_val_acc = max(history['val_acc']) if history['val_acc'] else 0.0
            
            self.logger.debug(f"参数 {params} 的验证准确率: {best_val_acc:.4f}")
            
            return best_val_acc
            
        except Exception as e:
            self.logger.error(f"评估参数 {params} 时出错: {str(e)}")
            return 0.0
    
    def _create_model_with_params(self, params: Dict[str, Any]) -> nn.Module:
        """
        根据参数创建模型
        
        Args:
            params (Dict[str, Any]): 模型参数
            
        Returns:
            nn.Module: 创建的模型
        """
        model_kwargs = {
            'num_classes': self.num_classes,
            'dropout_rate': params.get('dropout_rate', 0.2)
        }
        
        if self.model_type == 'cnn':
            model = CharacterRecognizer(**model_kwargs)
        elif self.model_type == 'crnn':
            model_kwargs.update({
                'hidden_size': params.get('hidden_size', 256),
                'num_layers': params.get('num_layers', 2)
            })
            model = CharacterRecognizerCRNN(**model_kwargs)
        elif self.model_type == 'transformer':
            model_kwargs.update({
                'hidden_size': params.get('hidden_size', 256),
                'num_layers': params.get('num_layers', 2)
            })
            model = CharacterRecognizerTransformer(**model_kwargs)
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")
        
        return model.to(self.device)
    
    def _save_optimization_results(self, results: List[Dict], 
                                 save_dir: str, method: str) -> None:
        """
        保存优化结果
        
        Args:
            results (List[Dict]): 优化结果
            save_dir (str): 保存目录
            method (str): 优化方法
        """
        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # 保存详细结果
        results_file = save_path / f'{method}_results.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 保存最佳结果
        best_result = max(results, key=lambda x: x['score'])
        best_file = save_path / f'{method}_best.json'
        with open(best_file, 'w', encoding='utf-8') as f:
            json.dump(best_result, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"优化结果已保存到 {save_path}")
    
    def plot_optimization_history(self, save_path: Optional[str] = None) -> None:
        """
        绘制优化历史
        
        Args:
            save_path (Optional[str]): 保存路径
        """
        if not self.optimization_history:
            self.logger.warning("没有优化历史数据")
            return
        
        import matplotlib.pyplot as plt
        
        trials = [result['trial'] for result in self.optimization_history]
        scores = [result['score'] for result in self.optimization_history]
        
        # 计算累积最佳分数
        cumulative_best = []
        best_so_far = 0.0
        for score in scores:
            best_so_far = max(best_so_far, score)
            cumulative_best.append(best_so_far)
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
        
        # 所有试验的分数
        ax1.scatter(trials, scores, alpha=0.6)
        ax1.set_xlabel('试验次数')
        ax1.set_ylabel('验证准确率')
        ax1.set_title('超参数优化历史')
        ax1.grid(True)
        
        # 累积最佳分数
        ax2.plot(trials, cumulative_best, 'r-', linewidth=2)
        ax2.set_xlabel('试验次数')
        ax2.set_ylabel('最佳验证准确率')
        ax2.set_title('累积最佳分数')
        ax2.grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"优化历史图表已保存: {save_path}")
        
        plt.show()
    
    def get_parameter_importance(self, results: List[Dict]) -> Dict[str, float]:
        """
        分析参数重要性
        
        Args:
            results (List[Dict]): 优化结果
            
        Returns:
            Dict[str, float]: 参数重要性分数
        """
        if not results:
            return {}
        
        # 提取参数和分数
        param_names = list(results[0]['params'].keys())
        param_importance = {}
        
        for param_name in param_names:
            # 计算该参数与分数的相关性
            param_values = []
            scores = []
            
            for result in results:
                param_value = result['params'][param_name]
                # 将分类参数转换为数值
                if isinstance(param_value, str):
                    param_value = hash(param_value) % 1000  # 简单的字符串到数值转换
                
                param_values.append(param_value)
                scores.append(result['score'])
            
            # 计算相关系数
            correlation = np.corrcoef(param_values, scores)[0, 1]
            param_importance[param_name] = abs(correlation) if not np.isnan(correlation) else 0.0
        
        # 归一化重要性分数
        total_importance = sum(param_importance.values())
        if total_importance > 0:
            param_importance = {k: v / total_importance for k, v in param_importance.items()}
        
        return param_importance


def create_default_param_distributions():
    """创建默认参数分布"""
    import random
    
    distributions = {
        'learning_rate': lambda: 10 ** random.uniform(-5, -2),
        'batch_size': lambda: random.choice([16, 32, 64, 128]),
        'weight_decay': lambda: 10 ** random.uniform(-6, -2),
        'optimizer': lambda: random.choice(['adam', 'adamw', 'sgd']),
        'scheduler': lambda: random.choice(['steplr', 'cosineannealinglr', 'reducelronplateau']),
        'dropout_rate': lambda: random.uniform(0.1, 0.5),
        'hidden_size': lambda: random.choice([128, 256, 512]),
        'num_layers': lambda: random.randint(1, 4)
    }
    
    return distributions


def create_default_param_grid():
    """创建默认参数网格"""
    param_grid = {
        'learning_rate': [0.001, 0.003, 0.01],
        'batch_size': [32, 64],
        'weight_decay': [1e-4, 1e-3],
        'optimizer': ['adam', 'adamw'],
        'scheduler': ['steplr', 'cosineannealinglr'],
        'dropout_rate': [0.2, 0.3, 0.4]
    }
    
    return param_grid
