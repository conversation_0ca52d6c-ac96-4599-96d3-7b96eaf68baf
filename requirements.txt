# 基于深度学习的车牌识别系统依赖包
# Core deep learning framework
torch>=1.8.0
torchvision>=0.9.0
torchaudio>=0.8.0

# Computer vision and image processing
opencv-python>=4.5.0
Pillow>=8.0.0
scikit-image>=0.18.0

# Scientific computing
numpy>=1.20.0
pandas>=1.3.0
matplotlib>=3.3.0
seaborn>=0.11.0

# Data augmentation
albumentations>=1.0.0
imgaug>=0.4.0

# Web scraping for data collection
requests>=2.25.0
beautifulsoup4>=4.9.0
selenium>=3.141.0
scrapy>=2.5.0

# Machine learning utilities
scikit-learn>=0.24.0
tqdm>=4.60.0

# GUI development
tkinter
PyQt5>=5.15.0

# Model serving and deployment
flask>=2.0.0
fastapi>=0.65.0
uvicorn>=0.13.0

# Configuration and logging
pyyaml>=5.4.0
configparser>=5.0.0
loguru>=0.5.0

# Development and testing
pytest>=6.2.0
jupyter>=1.0.0
ipython>=7.20.0

# GPU acceleration (optional)
# Uncomment if CUDA is available
# torch-audio
# torch-vision
