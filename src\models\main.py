#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型主模块
Models Main Module

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path
import argparse
import json
import yaml

from ..utils.logger import LoggerMixin
from ..utils.config_loader import load_config
from .character_recognizer import CharacterRecognizer, CharacterRecognizerCRNN, CharacterRecognizerTransformer
from .model_trainer import ModelTrainer
from .model_evaluator import ModelEvaluator
from .character_dataset import (
    CharacterDataset, SequenceCharacterDataset, CharacterDatasetAnalyzer,
    create_character_transforms, create_data_loaders
)


class ModelManager(LoggerMixin):
    """模型管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化模型管理器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.model_config = config.get('model', {})
        self.training_config = config.get('training', {})
        
        # 设备配置
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 字符映射
        self.char_mapping = self._load_character_mapping()
        self.num_classes = len(self.char_mapping)
        
        # 模型
        self.model = None
        self.trainer = None
        self.evaluator = None
        
        self.logger.info(f"模型管理器初始化完成，字符类别数: {self.num_classes}")
    
    def _load_character_mapping(self) -> Dict[str, int]:
        """
        加载字符映射
        
        Returns:
            Dict[str, int]: 字符到索引的映射
        """
        mapping_file = self.config.get('data', {}).get('char_mapping_file', 'data/char_mapping.json')
        mapping_path = Path(mapping_file)
        
        if mapping_path.exists():
            with open(mapping_path, 'r', encoding='utf-8') as f:
                char_mapping = json.load(f)
            self.logger.info(f"从文件加载字符映射: {mapping_path}")
        else:
            # 创建默认字符映射
            char_mapping = self._create_default_character_mapping()
            
            # 保存映射文件
            mapping_path.parent.mkdir(parents=True, exist_ok=True)
            with open(mapping_path, 'w', encoding='utf-8') as f:
                json.dump(char_mapping, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"创建并保存默认字符映射: {mapping_path}")
        
        return char_mapping
    
    def _create_default_character_mapping(self) -> Dict[str, int]:
        """
        创建默认字符映射
        
        Returns:
            Dict[str, int]: 默认字符映射
        """
        # 中国车牌字符集
        provinces = ['京', '津', '沪', '渝', '冀', '豫', '云', '辽', '黑', '湘', '皖', '鲁', 
                    '新', '苏', '浙', '赣', '鄂', '桂', '甘', '晋', '蒙', '陕', '吉', '闽', 
                    '贵', '粤', '青', '藏', '川', '宁', '琼']
        
        letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 
                  'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
        
        digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
        
        # 组合所有字符
        all_chars = provinces + letters + digits
        
        # 创建映射（索引0保留给空白字符）
        char_mapping = {}
        for i, char in enumerate(all_chars, start=1):
            char_mapping[char] = i
        
        return char_mapping
    
    def create_model(self, model_type: str = 'cnn', **kwargs) -> nn.Module:
        """
        创建模型
        
        Args:
            model_type (str): 模型类型
            **kwargs: 额外参数
            
        Returns:
            nn.Module: 创建的模型
        """
        if model_type.lower() == 'cnn':
            self.model = CharacterRecognizer(
                num_classes=self.num_classes,
                **kwargs
            )
        elif model_type.lower() == 'crnn':
            self.model = CharacterRecognizerCRNN(
                num_classes=self.num_classes,
                **kwargs
            )
        elif model_type.lower() == 'transformer':
            self.model = CharacterRecognizerTransformer(
                num_classes=self.num_classes,
                **kwargs
            )
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        self.model.to(self.device)
        
        # 创建训练器和评估器
        self.trainer = ModelTrainer(self.model, self.config)
        self.evaluator = ModelEvaluator(self.model, self.device)
        
        self.logger.info(f"创建 {model_type.upper()} 模型，参数量: {self.model.count_parameters():,}")
        
        return self.model
    
    def load_model(self, model_path: str, model_type: str = 'cnn') -> nn.Module:
        """
        加载模型
        
        Args:
            model_path (str): 模型路径
            model_type (str): 模型类型
            
        Returns:
            nn.Module: 加载的模型
        """
        # 创建模型
        self.create_model(model_type)
        
        # 加载权重
        checkpoint = torch.load(model_path, map_location=self.device)
        
        if 'model_state_dict' in checkpoint:
            self.model.load_state_dict(checkpoint['model_state_dict'])
        else:
            self.model.load_state_dict(checkpoint)
        
        self.logger.info(f"从 {model_path} 加载模型")
        
        return self.model
    
    def prepare_datasets(self, data_dir: str, 
                        train_ratio: float = 0.7,
                        val_ratio: float = 0.2) -> Tuple[CharacterDataset, CharacterDataset, CharacterDataset]:
        """
        准备数据集
        
        Args:
            data_dir (str): 数据目录
            train_ratio (float): 训练集比例
            val_ratio (float): 验证集比例
            
        Returns:
            Tuple[CharacterDataset, CharacterDataset, CharacterDataset]: 训练、验证、测试数据集
        """
        data_path = Path(data_dir)
        
        # 创建数据变换
        train_transform = create_character_transforms(training=True)
        val_transform = create_character_transforms(training=False)
        
        # 加载完整数据集
        full_dataset = CharacterDataset(
            data_dir=str(data_path),
            char_mapping=self.char_mapping,
            transform=None  # 先不应用变换，用于分割
        )
        
        # 分割数据集
        total_size = len(full_dataset)
        train_size = int(total_size * train_ratio)
        val_size = int(total_size * val_ratio)
        test_size = total_size - train_size - val_size
        
        # 随机分割
        indices = torch.randperm(total_size).tolist()
        train_indices = indices[:train_size]
        val_indices = indices[train_size:train_size + val_size]
        test_indices = indices[train_size + val_size:]
        
        # 创建子数据集
        train_samples = [full_dataset.samples[i] for i in train_indices]
        val_samples = [full_dataset.samples[i] for i in val_indices]
        test_samples = [full_dataset.samples[i] for i in test_indices]
        
        # 创建数据集对象
        train_dataset = CharacterDataset(data_dir, self.char_mapping, train_transform)
        train_dataset.samples = train_samples
        
        val_dataset = CharacterDataset(data_dir, self.char_mapping, val_transform)
        val_dataset.samples = val_samples
        
        test_dataset = CharacterDataset(data_dir, self.char_mapping, val_transform)
        test_dataset.samples = test_samples
        
        self.logger.info(f"数据集准备完成 - 训练: {len(train_dataset)}, 验证: {len(val_dataset)}, 测试: {len(test_dataset)}")
        
        return train_dataset, val_dataset, test_dataset
    
    def train_model(self, train_dataset: CharacterDataset,
                   val_dataset: CharacterDataset,
                   save_dir: str) -> Dict[str, List[float]]:
        """
        训练模型
        
        Args:
            train_dataset (CharacterDataset): 训练数据集
            val_dataset (CharacterDataset): 验证数据集
            save_dir (str): 保存目录
            
        Returns:
            Dict[str, List[float]]: 训练历史
        """
        if self.model is None:
            raise ValueError("请先创建或加载模型")
        
        # 创建数据加载器
        batch_size = self.training_config.get('batch_size', 32)
        num_workers = self.training_config.get('num_workers', 4)
        
        train_loader, val_loader = create_data_loaders(
            train_dataset, val_dataset, batch_size, num_workers
        )
        
        # 设置训练组件
        optimizer_type = self.training_config.get('optimizer', 'adam')
        criterion_type = self.training_config.get('criterion', 'crossentropy')
        scheduler_type = self.training_config.get('scheduler', 'steplr')
        
        self.trainer.setup_training(optimizer_type, criterion_type, scheduler_type)
        
        # 开始训练
        history = self.trainer.train(train_loader, val_loader, save_dir)
        
        # 绘制训练历史
        save_path = Path(save_dir)
        self.trainer.plot_training_history(str(save_path / 'training_history.png'))
        
        return history
    
    def evaluate_model(self, test_dataset: CharacterDataset,
                      save_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        评估模型
        
        Args:
            test_dataset (CharacterDataset): 测试数据集
            save_dir (Optional[str]): 保存目录
            
        Returns:
            Dict[str, Any]: 评估结果
        """
        if self.model is None:
            raise ValueError("请先创建或加载模型")
        
        # 创建测试数据加载器
        batch_size = self.training_config.get('batch_size', 32)
        test_loader = DataLoader(
            test_dataset, batch_size=batch_size, shuffle=False, num_workers=4
        )
        
        # 创建类别名称列表
        reverse_mapping = {v: k for k, v in self.char_mapping.items()}
        class_names = [reverse_mapping.get(i, f'unknown_{i}') for i in range(self.num_classes)]
        
        # 评估模型
        results = self.evaluator.evaluate_classification(test_loader, class_names)
        
        # 保存结果
        if save_dir:
            save_path = Path(save_dir)
            save_path.mkdir(parents=True, exist_ok=True)
            
            # 保存评估结果
            with open(save_path / 'evaluation_results.json', 'w', encoding='utf-8') as f:
                # 移除不能序列化的数据
                serializable_results = {k: v for k, v in results.items() 
                                      if k not in ['predictions', 'targets', 'probabilities']}
                json.dump(serializable_results, f, ensure_ascii=False, indent=2)
            
            # 生成分类报告
            report = self.evaluator.generate_classification_report(results)
            with open(save_path / 'classification_report.txt', 'w', encoding='utf-8') as f:
                f.write(report)
            
            # 绘制图表
            self.evaluator.plot_confusion_matrix(
                np.array(results['confusion_matrix']), 
                class_names, 
                str(save_path / 'confusion_matrix.png')
            )
            
            self.evaluator.plot_class_performance(
                results, 
                str(save_path / 'class_performance.png')
            )
            
            self.logger.info(f"评估结果已保存到 {save_path}")
        
        return results
    
    def predict_characters(self, images: List[np.ndarray]) -> List[Tuple[str, float]]:
        """
        预测字符
        
        Args:
            images (List[np.ndarray]): 字符图像列表
            
        Returns:
            List[Tuple[str, float]]: 预测结果和置信度
        """
        if self.model is None:
            raise ValueError("请先创建或加载模型")
        
        self.model.eval()
        
        # 创建反向映射
        reverse_mapping = {v: k for k, v in self.char_mapping.items()}
        
        results = []
        
        with torch.no_grad():
            for image in images:
                # 预处理图像
                processed_image = self._preprocess_image(image)
                processed_image = processed_image.unsqueeze(0).to(self.device)
                
                # 预测
                output = self.model(processed_image)
                probabilities = torch.softmax(output, dim=1)
                
                # 获取预测结果
                confidence, predicted = torch.max(probabilities, 1)
                predicted_char = reverse_mapping.get(predicted.item(), 'unknown')
                
                results.append((predicted_char, confidence.item()))
        
        return results
    
    def _preprocess_image(self, image: np.ndarray) -> torch.Tensor:
        """
        预处理单张图像
        
        Args:
            image (np.ndarray): 输入图像
            
        Returns:
            torch.Tensor: 预处理后的图像张量
        """
        # 使用验证时的变换
        transform = create_character_transforms(training=False)
        
        # 转换为PIL图像
        from PIL import Image
        if len(image.shape) == 3:
            pil_image = Image.fromarray(image)
        else:
            pil_image = Image.fromarray(image).convert('RGB')
        
        # 应用变换
        tensor_image = transform(pil_image)
        
        return tensor_image
    
    def analyze_dataset(self, dataset: CharacterDataset, 
                       save_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        分析数据集
        
        Args:
            dataset (CharacterDataset): 数据集
            save_dir (Optional[str]): 保存目录
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        analyzer = CharacterDatasetAnalyzer(dataset)
        analysis = analyzer.analyze_dataset()
        
        if save_dir:
            save_path = Path(save_dir)
            save_path.mkdir(parents=True, exist_ok=True)
            
            # 保存分析结果
            with open(save_path / 'dataset_analysis.json', 'w', encoding='utf-8') as f:
                json.dump(analysis, f, ensure_ascii=False, indent=2)
            
            # 绘制类别分布图
            analyzer.plot_class_distribution(str(save_path / 'class_distribution.png'))
            
            self.logger.info(f"数据集分析结果已保存到 {save_path}")
        
        return analysis


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='字符识别模型管理')
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--mode', type=str, choices=['train', 'evaluate', 'predict', 'analyze'],
                       required=True, help='运行模式')
    parser.add_argument('--model-type', type=str, choices=['cnn', 'crnn', 'transformer'],
                       default='cnn', help='模型类型')
    parser.add_argument('--data-dir', type=str, required=True,
                       help='数据目录路径')
    parser.add_argument('--model-path', type=str,
                       help='模型路径（用于加载已训练模型）')
    parser.add_argument('--save-dir', type=str, default='outputs/models',
                       help='保存目录')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 创建模型管理器
    manager = ModelManager(config)
    
    if args.mode == 'train':
        # 训练模式
        if args.model_path:
            manager.load_model(args.model_path, args.model_type)
        else:
            manager.create_model(args.model_type)
        
        # 准备数据集
        train_dataset, val_dataset, test_dataset = manager.prepare_datasets(args.data_dir)
        
        # 训练模型
        history = manager.train_model(train_dataset, val_dataset, args.save_dir)
        
        print(f"训练完成，最佳验证准确率: {manager.trainer.best_val_acc:.4f}")
    
    elif args.mode == 'evaluate':
        # 评估模式
        if not args.model_path:
            raise ValueError("评估模式需要指定模型路径")
        
        manager.load_model(args.model_path, args.model_type)
        
        # 准备测试数据集
        _, _, test_dataset = manager.prepare_datasets(args.data_dir)
        
        # 评估模型
        results = manager.evaluate_model(test_dataset, args.save_dir)
        
        print(f"评估完成，准确率: {results['overall_accuracy']:.4f}")
    
    elif args.mode == 'analyze':
        # 分析模式
        train_dataset, val_dataset, test_dataset = manager.prepare_datasets(args.data_dir)
        
        # 分析数据集
        analysis = manager.analyze_dataset(train_dataset, args.save_dir)
        
        print(f"数据集分析完成，共 {analysis['total_samples']} 个样本，{analysis['num_classes']} 个类别")
    
    else:
        print(f"不支持的模式: {args.mode}")


if __name__ == '__main__':
    main()
