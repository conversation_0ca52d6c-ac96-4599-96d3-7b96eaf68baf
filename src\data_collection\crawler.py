#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车牌图像数据采集爬虫
License Plate Image Data Crawler

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import os
import time
import requests
import hashlib
from pathlib import Path
from typing import List, Dict, Any
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import cv2
import numpy as np
from tqdm import tqdm

from ..utils.logger import LoggerMixin


class LicensePlateImageCrawler(LoggerMixin):
    """车牌图像爬虫类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化爬虫
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.data_config = config.get('data', {})
        self.raw_data_path = Path(self.data_config.get('raw_data_path', 'data/raw'))
        self.raw_data_path.mkdir(parents=True, exist_ok=True)
        
        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON>HTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        # 创建session
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 支持的图像格式
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.webp'}
        
        # 已下载图像的哈希集合，用于去重
        self.downloaded_hashes = set()
        
        self.logger.info("车牌图像爬虫初始化完成")
    
    def crawl_from_search_engines(self, keywords: List[str], max_images: int = 1000) -> int:
        """
        从搜索引擎爬取车牌图像
        
        Args:
            keywords (List[str]): 搜索关键词列表
            max_images (int): 最大图像数量
            
        Returns:
            int: 成功下载的图像数量
        """
        total_downloaded = 0
        
        for keyword in keywords:
            self.logger.info(f"开始搜索关键词: {keyword}")
            
            # 从百度图片搜索
            downloaded = self._crawl_baidu_images(keyword, max_images // len(keywords))
            total_downloaded += downloaded
            
            # 添加延时避免被封
            time.sleep(2)
            
            if total_downloaded >= max_images:
                break
        
        self.logger.info(f"搜索引擎爬取完成，共下载 {total_downloaded} 张图像")
        return total_downloaded
    
    def _crawl_baidu_images(self, keyword: str, max_images: int) -> int:
        """
        从百度图片搜索爬取图像
        
        Args:
            keyword (str): 搜索关键词
            max_images (int): 最大图像数量
            
        Returns:
            int: 下载的图像数量
        """
        downloaded_count = 0
        page = 0
        
        while downloaded_count < max_images:
            try:
                # 构建搜索URL
                search_url = f"https://image.baidu.com/search/flip?tn=baiduimage&word={keyword}&pn={page * 20}"
                
                response = self.session.get(search_url, timeout=10)
                response.raise_for_status()
                
                # 解析页面
                soup = BeautifulSoup(response.text, 'html.parser')
                img_tags = soup.find_all('img', {'src': True})
                
                if not img_tags:
                    self.logger.warning(f"关键词 {keyword} 第 {page} 页未找到图像")
                    break
                
                # 下载图像
                for img_tag in img_tags:
                    if downloaded_count >= max_images:
                        break
                    
                    img_url = img_tag.get('src')
                    if img_url and self._is_valid_image_url(img_url):
                        if self._download_image(img_url, keyword):
                            downloaded_count += 1
                
                page += 1
                time.sleep(1)  # 添加延时
                
            except Exception as e:
                self.logger.error(f"爬取百度图片出错: {str(e)}")
                break
        
        return downloaded_count
    
    def crawl_from_datasets(self, dataset_urls: List[str]) -> int:
        """
        从公开数据集下载车牌图像
        
        Args:
            dataset_urls (List[str]): 数据集URL列表
            
        Returns:
            int: 下载的图像数量
        """
        total_downloaded = 0
        
        for url in dataset_urls:
            self.logger.info(f"开始下载数据集: {url}")
            
            try:
                # 这里可以添加具体的数据集下载逻辑
                # 例如从GitHub、Kaggle等平台下载
                downloaded = self._download_dataset(url)
                total_downloaded += downloaded
                
            except Exception as e:
                self.logger.error(f"下载数据集失败 {url}: {str(e)}")
        
        return total_downloaded
    
    def _download_dataset(self, url: str) -> int:
        """
        下载单个数据集
        
        Args:
            url (str): 数据集URL
            
        Returns:
            int: 下载的图像数量
        """
        # 这里实现具体的数据集下载逻辑
        # 可以根据不同的数据集格式进行处理
        self.logger.info(f"数据集下载功能待实现: {url}")
        return 0
    
    def _is_valid_image_url(self, url: str) -> bool:
        """
        检查URL是否为有效的图像URL
        
        Args:
            url (str): 图像URL
            
        Returns:
            bool: 是否有效
        """
        if not url or not url.startswith(('http://', 'https://')):
            return False
        
        parsed_url = urlparse(url)
        path = parsed_url.path.lower()
        
        return any(path.endswith(fmt) for fmt in self.supported_formats)
    
    def _download_image(self, url: str, keyword: str) -> bool:
        """
        下载单张图像
        
        Args:
            url (str): 图像URL
            keyword (str): 关键词（用于分类存储）
            
        Returns:
            bool: 是否下载成功
        """
        try:
            response = self.session.get(url, timeout=10, stream=True)
            response.raise_for_status()
            
            # 检查内容类型
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                return False
            
            # 读取图像数据
            image_data = response.content
            
            # 计算哈希值用于去重
            image_hash = hashlib.md5(image_data).hexdigest()
            if image_hash in self.downloaded_hashes:
                return False
            
            # 验证图像是否有效
            if not self._is_valid_image_data(image_data):
                return False
            
            # 保存图像
            keyword_dir = self.raw_data_path / keyword
            keyword_dir.mkdir(exist_ok=True)
            
            # 生成文件名
            file_extension = self._get_file_extension(url, content_type)
            filename = f"{image_hash}{file_extension}"
            file_path = keyword_dir / filename
            
            with open(file_path, 'wb') as f:
                f.write(image_data)
            
            self.downloaded_hashes.add(image_hash)
            self.logger.debug(f"下载图像成功: {filename}")
            return True
            
        except Exception as e:
            self.logger.debug(f"下载图像失败 {url}: {str(e)}")
            return False
    
    def _is_valid_image_data(self, image_data: bytes) -> bool:
        """
        验证图像数据是否有效
        
        Args:
            image_data (bytes): 图像数据
            
        Returns:
            bool: 是否有效
        """
        try:
            # 使用OpenCV验证图像
            nparr = np.frombuffer(image_data, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if img is None:
                return False
            
            # 检查图像尺寸
            height, width = img.shape[:2]
            if height < 50 or width < 50:  # 过小的图像
                return False
            
            if height > 5000 or width > 5000:  # 过大的图像
                return False
            
            return True
            
        except Exception:
            return False
    
    def _get_file_extension(self, url: str, content_type: str) -> str:
        """
        获取文件扩展名
        
        Args:
            url (str): 图像URL
            content_type (str): 内容类型
            
        Returns:
            str: 文件扩展名
        """
        # 首先尝试从URL获取扩展名
        parsed_url = urlparse(url)
        path = parsed_url.path.lower()
        
        for fmt in self.supported_formats:
            if path.endswith(fmt):
                return fmt
        
        # 从content-type获取扩展名
        if 'jpeg' in content_type or 'jpg' in content_type:
            return '.jpg'
        elif 'png' in content_type:
            return '.png'
        elif 'bmp' in content_type:
            return '.bmp'
        elif 'webp' in content_type:
            return '.webp'
        
        return '.jpg'  # 默认扩展名


def run_data_collection(config: Dict[str, Any]) -> None:
    """
    运行数据采集
    
    Args:
        config (Dict[str, Any]): 配置字典
    """
    crawler = LicensePlateImageCrawler(config)
    
    # 定义搜索关键词
    keywords = [
        "车牌",
        "汽车牌照",
        "license plate",
        "car plate",
        "vehicle plate",
        "蓝色车牌",
        "黄色车牌",
        "绿色车牌",
        "新能源车牌"
    ]
    
    # 从搜索引擎爬取
    crawler.crawl_from_search_engines(keywords, max_images=5000)
    
    # 可以添加更多数据源
    # dataset_urls = [
    #     "https://github.com/example/license-plate-dataset",
    #     "https://www.kaggle.com/datasets/example/license-plates"
    # ]
    # crawler.crawl_from_datasets(dataset_urls)
