#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字符分割主模块
Character Segmentation Main Module

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import cv2
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import argparse
import json

from ..utils.logger import LoggerMixin
from ..utils.config_loader import load_config
from .character_segmenter import CharacterSegmenter, AdaptiveCharacterSegmenter
from .segmentation_utils import SegmentationUtils


class CharacterSegmentationManager(LoggerMixin):
    """字符分割管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化字符分割管理器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.seg_config = config.get('segmentation', {})
        
        # 初始化分割器
        self.segmenter_type = self.seg_config.get('segmenter_type', 'adaptive')
        
        if self.segmenter_type == 'adaptive':
            self.segmenter = AdaptiveCharacterSegmenter(config)
        else:
            self.segmenter = CharacterSegmenter(config)
        
        # 工具类
        self.utils = SegmentationUtils()
        
        self.logger.info(f"字符分割管理器初始化完成，使用分割器: {self.segmenter_type}")
    
    def segment_plate_characters(self, plate_image: np.ndarray, 
                               debug: bool = False) -> List[np.ndarray]:
        """
        分割车牌字符
        
        Args:
            plate_image (np.ndarray): 车牌图像
            debug (bool): 是否启用调试模式
            
        Returns:
            List[np.ndarray]: 分割后的字符图像列表
        """
        if plate_image is None or plate_image.size == 0:
            self.logger.warning("输入的车牌图像为空")
            return []
        
        try:
            # 使用分割器进行字符分割
            char_images = self.segmenter.segment_characters(plate_image, debug=debug)
            
            # 后处理
            processed_chars = self._post_process_characters(char_images)
            
            self.logger.info(f"字符分割完成，共分割出 {len(processed_chars)} 个字符")
            return processed_chars
            
        except Exception as e:
            self.logger.error(f"字符分割失败: {str(e)}")
            return []
    
    def batch_segment_characters(self, plate_images: List[np.ndarray],
                               debug: bool = False) -> List[List[np.ndarray]]:
        """
        批量分割字符
        
        Args:
            plate_images (List[np.ndarray]): 车牌图像列表
            debug (bool): 是否启用调试模式
            
        Returns:
            List[List[np.ndarray]]: 批量分割结果
        """
        results = []
        
        for i, plate_image in enumerate(plate_images):
            self.logger.info(f"处理第 {i+1}/{len(plate_images)} 张车牌图像")
            
            char_images = self.segment_plate_characters(plate_image, debug=debug)
            results.append(char_images)
        
        self.logger.info(f"批量字符分割完成，处理了 {len(plate_images)} 张图像")
        return results
    
    def segment_from_file(self, image_path: str, 
                         output_dir: Optional[str] = None,
                         debug: bool = False) -> List[np.ndarray]:
        """
        从文件分割字符
        
        Args:
            image_path (str): 图像文件路径
            output_dir (Optional[str]): 输出目录
            debug (bool): 是否启用调试模式
            
        Returns:
            List[np.ndarray]: 分割后的字符图像列表
        """
        # 读取图像
        plate_image = cv2.imread(image_path)
        if plate_image is None:
            self.logger.error(f"无法读取图像文件: {image_path}")
            return []
        
        # 分割字符
        char_images = self.segment_plate_characters(plate_image, debug=debug)
        
        # 保存结果
        if output_dir and char_images:
            self._save_segmented_characters(char_images, image_path, output_dir)
        
        return char_images
    
    def segment_from_directory(self, input_dir: str, 
                             output_dir: str,
                             debug: bool = False) -> Dict[str, List[np.ndarray]]:
        """
        从目录批量分割字符
        
        Args:
            input_dir (str): 输入目录
            output_dir (str): 输出目录
            debug (bool): 是否启用调试模式
            
        Returns:
            Dict[str, List[np.ndarray]]: 分割结果字典
        """
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 支持的图像格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        
        # 获取所有图像文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(input_path.glob(f'*{ext}'))
            image_files.extend(input_path.glob(f'*{ext.upper()}'))
        
        if not image_files:
            self.logger.warning(f"在目录 {input_dir} 中未找到图像文件")
            return {}
        
        results = {}
        
        for image_file in image_files:
            self.logger.info(f"处理图像: {image_file.name}")
            
            try:
                char_images = self.segment_from_file(
                    str(image_file), str(output_path), debug=debug
                )
                results[image_file.name] = char_images
                
            except Exception as e:
                self.logger.error(f"处理图像 {image_file.name} 失败: {str(e)}")
                results[image_file.name] = []
        
        # 保存统计信息
        self._save_segmentation_statistics(results, output_path)
        
        self.logger.info(f"批量字符分割完成，处理了 {len(image_files)} 张图像")
        return results
    
    def evaluate_segmentation(self, test_data: List[Tuple[np.ndarray, List[Tuple[int, int]]]],
                            iou_threshold: float = 0.5) -> Dict[str, float]:
        """
        评估分割性能
        
        Args:
            test_data (List[Tuple[np.ndarray, List[Tuple[int, int]]]]): 测试数据
            iou_threshold (float): IoU阈值
            
        Returns:
            Dict[str, float]: 评估指标
        """
        all_metrics = []
        
        for plate_image, ground_truth_regions in test_data:
            # 分割字符
            char_images = self.segment_plate_characters(plate_image)
            
            # 获取预测区域（需要从字符图像反推区域坐标）
            predicted_regions = self._extract_regions_from_chars(char_images, plate_image)
            
            # 计算指标
            metrics = self.utils.calculate_segmentation_metrics(
                predicted_regions, ground_truth_regions, iou_threshold
            )
            all_metrics.append(metrics)
        
        # 计算平均指标
        avg_metrics = {}
        if all_metrics:
            for key in all_metrics[0].keys():
                if isinstance(all_metrics[0][key], (int, float)):
                    avg_metrics[key] = np.mean([m[key] for m in all_metrics])
        
        self.logger.info(f"分割评估完成，平均F1分数: {avg_metrics.get('f1_score', 0.0):.3f}")
        return avg_metrics
    
    def _post_process_characters(self, char_images: List[np.ndarray]) -> List[np.ndarray]:
        """
        后处理字符图像
        
        Args:
            char_images (List[np.ndarray]): 原始字符图像列表
            
        Returns:
            List[np.ndarray]: 后处理后的字符图像列表
        """
        processed_chars = []
        
        for char_image in char_images:
            # 质量检查
            if self._is_valid_character(char_image):
                # 图像增强
                enhanced_char = self._enhance_character_image(char_image)
                processed_chars.append(enhanced_char)
        
        return processed_chars
    
    def _is_valid_character(self, char_image: np.ndarray) -> bool:
        """
        检查字符图像是否有效
        
        Args:
            char_image (np.ndarray): 字符图像
            
        Returns:
            bool: 是否有效
        """
        if char_image is None or char_image.size == 0:
            return False
        
        # 检查尺寸
        height, width = char_image.shape[:2]
        if height < 10 or width < 5:
            return False
        
        # 检查内容
        if len(char_image.shape) == 3:
            gray = cv2.cvtColor(char_image, cv2.COLOR_BGR2GRAY)
        else:
            gray = char_image
        
        # 检查是否有足够的前景像素
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        foreground_ratio = np.sum(binary == 0) / binary.size
        
        return 0.05 <= foreground_ratio <= 0.8
    
    def _enhance_character_image(self, char_image: np.ndarray) -> np.ndarray:
        """
        增强字符图像
        
        Args:
            char_image (np.ndarray): 原始字符图像
            
        Returns:
            np.ndarray: 增强后的字符图像
        """
        # 转换为灰度图
        if len(char_image.shape) == 3:
            gray = cv2.cvtColor(char_image, cv2.COLOR_BGR2GRAY)
        else:
            gray = char_image.copy()
        
        # 直方图均衡化
        enhanced = cv2.equalizeHist(gray)
        
        # 高斯模糊去噪
        enhanced = cv2.GaussianBlur(enhanced, (3, 3), 0)
        
        # 锐化
        kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
        enhanced = cv2.filter2D(enhanced, -1, kernel)
        
        # 转换回原始格式
        if len(char_image.shape) == 3:
            enhanced = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)
        
        return enhanced
    
    def _save_segmented_characters(self, char_images: List[np.ndarray], 
                                 original_path: str, output_dir: str) -> None:
        """
        保存分割后的字符图像
        
        Args:
            char_images (List[np.ndarray]): 字符图像列表
            original_path (str): 原始图像路径
            output_dir (str): 输出目录
        """
        output_path = Path(output_dir)
        original_name = Path(original_path).stem
        
        # 创建子目录
        char_dir = output_path / original_name
        char_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存每个字符
        for i, char_image in enumerate(char_images):
            char_filename = char_dir / f"char_{i:02d}.png"
            cv2.imwrite(str(char_filename), char_image)
        
        self.logger.debug(f"已保存 {len(char_images)} 个字符到 {char_dir}")
    
    def _extract_regions_from_chars(self, char_images: List[np.ndarray], 
                                  original_image: np.ndarray) -> List[Tuple[int, int]]:
        """
        从字符图像反推区域坐标（简化实现）
        
        Args:
            char_images (List[np.ndarray]): 字符图像列表
            original_image (np.ndarray): 原始图像
            
        Returns:
            List[Tuple[int, int]]: 区域坐标列表
        """
        # 这是一个简化的实现，实际应用中需要更精确的方法
        regions = []
        total_width = original_image.shape[1]
        
        if char_images:
            char_width = total_width // len(char_images)
            for i in range(len(char_images)):
                start_x = i * char_width
                end_x = (i + 1) * char_width
                regions.append((start_x, end_x))
        
        return regions
    
    def _save_segmentation_statistics(self, results: Dict[str, List[np.ndarray]], 
                                    output_dir: Path) -> None:
        """
        保存分割统计信息
        
        Args:
            results (Dict[str, List[np.ndarray]]): 分割结果
            output_dir (Path): 输出目录
        """
        stats = {
            'total_images': len(results),
            'successful_segmentations': sum(1 for chars in results.values() if chars),
            'total_characters': sum(len(chars) for chars in results.values()),
            'average_characters_per_image': 0.0,
            'character_count_distribution': {}
        }
        
        # 计算平均字符数
        if stats['total_images'] > 0:
            stats['average_characters_per_image'] = stats['total_characters'] / stats['total_images']
        
        # 统计字符数分布
        for chars in results.values():
            count = len(chars)
            stats['character_count_distribution'][count] = (
                stats['character_count_distribution'].get(count, 0) + 1
            )
        
        # 保存统计信息
        stats_file = output_dir / 'segmentation_statistics.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"分割统计信息已保存到 {stats_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='车牌字符分割')
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--input', type=str, required=True,
                       help='输入图像或目录路径')
    parser.add_argument('--output', type=str, required=True,
                       help='输出目录路径')
    parser.add_argument('--debug', action='store_true',
                       help='启用调试模式')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 创建分割管理器
    manager = CharacterSegmentationManager(config)
    
    # 处理输入
    input_path = Path(args.input)
    
    if input_path.is_file():
        # 单个文件
        manager.segment_from_file(str(input_path), args.output, debug=args.debug)
    elif input_path.is_dir():
        # 目录
        manager.segment_from_directory(str(input_path), args.output, debug=args.debug)
    else:
        print(f"错误: 输入路径不存在 - {args.input}")


if __name__ == '__main__':
    main()
