#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分割工具类
Segmentation Utilities

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict, Any
from scipy import ndimage
from skimage import measure, morphology
import matplotlib.pyplot as plt

from ..utils.logger import LoggerMixin


class SegmentationUtils(LoggerMixin):
    """字符分割工具类"""
    
    def __init__(self):
        """初始化分割工具类"""
        pass
    
    @staticmethod
    def calculate_vertical_projection(binary_image: np.ndarray) -> np.ndarray:
        """
        计算垂直投影
        
        Args:
            binary_image (np.ndarray): 二值化图像
            
        Returns:
            np.ndarray: 垂直投影数组
        """
        return np.sum(binary_image, axis=0)
    
    @staticmethod
    def calculate_horizontal_projection(binary_image: np.ndarray) -> np.ndarray:
        """
        计算水平投影
        
        Args:
            binary_image (np.ndarray): 二值化图像
            
        Returns:
            np.ndarray: 水平投影数组
        """
        return np.sum(binary_image, axis=1)
    
    @staticmethod
    def smooth_projection(projection: np.ndarray, kernel_size: int = 5) -> np.ndarray:
        """
        平滑投影曲线
        
        Args:
            projection (np.ndarray): 原始投影
            kernel_size (int): 平滑核大小
            
        Returns:
            np.ndarray: 平滑后的投影
        """
        if kernel_size % 2 == 0:
            kernel_size += 1
        
        return cv2.GaussianBlur(
            projection.astype(np.float32).reshape(1, -1), 
            (kernel_size, 1), 0
        ).flatten()
    
    @staticmethod
    def find_valleys(projection: np.ndarray, min_valley_depth: float = 0.1) -> List[int]:
        """
        寻找投影曲线中的谷值点
        
        Args:
            projection (np.ndarray): 投影数组
            min_valley_depth (float): 最小谷深度比例
            
        Returns:
            List[int]: 谷值点位置列表
        """
        # 计算阈值
        max_val = np.max(projection)
        min_val = np.min(projection)
        threshold = min_val + (max_val - min_val) * min_valley_depth
        
        # 寻找局部最小值
        valleys = []
        for i in range(1, len(projection) - 1):
            if (projection[i] < projection[i-1] and 
                projection[i] < projection[i+1] and 
                projection[i] < threshold):
                valleys.append(i)
        
        return valleys
    
    @staticmethod
    def find_peaks(projection: np.ndarray, min_peak_height: float = 0.3) -> List[int]:
        """
        寻找投影曲线中的峰值点
        
        Args:
            projection (np.ndarray): 投影数组
            min_peak_height (float): 最小峰高度比例
            
        Returns:
            List[int]: 峰值点位置列表
        """
        # 计算阈值
        max_val = np.max(projection)
        min_val = np.min(projection)
        threshold = min_val + (max_val - min_val) * min_peak_height
        
        # 寻找局部最大值
        peaks = []
        for i in range(1, len(projection) - 1):
            if (projection[i] > projection[i-1] and 
                projection[i] > projection[i+1] and 
                projection[i] > threshold):
                peaks.append(i)
        
        return peaks
    
    @staticmethod
    def remove_noise_regions(binary_image: np.ndarray, 
                           min_area: int = 10) -> np.ndarray:
        """
        移除噪声区域
        
        Args:
            binary_image (np.ndarray): 二值化图像
            min_area (int): 最小区域面积
            
        Returns:
            np.ndarray: 去噪后的图像
        """
        # 连通组件分析
        labeled_image = measure.label(binary_image)
        regions = measure.regionprops(labeled_image)
        
        # 创建输出图像
        cleaned_image = np.zeros_like(binary_image)
        
        # 保留大于最小面积的区域
        for region in regions:
            if region.area >= min_area:
                coords = region.coords
                cleaned_image[coords[:, 0], coords[:, 1]] = 255
        
        return cleaned_image
    
    @staticmethod
    def separate_touching_characters(binary_image: np.ndarray, 
                                   char_width_estimate: int) -> np.ndarray:
        """
        分离粘连字符
        
        Args:
            binary_image (np.ndarray): 二值化图像
            char_width_estimate (int): 估计的字符宽度
            
        Returns:
            np.ndarray: 分离后的图像
        """
        # 使用形态学开运算
        kernel_width = max(1, char_width_estimate // 4)
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_width, 1))
        opened = cv2.morphologyEx(binary_image, cv2.MORPH_OPEN, kernel)
        
        return opened
    
    @staticmethod
    def enhance_character_boundaries(binary_image: np.ndarray) -> np.ndarray:
        """
        增强字符边界
        
        Args:
            binary_image (np.ndarray): 二值化图像
            
        Returns:
            np.ndarray: 边界增强后的图像
        """
        # 使用形态学梯度
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        gradient = cv2.morphologyEx(binary_image, cv2.MORPH_GRADIENT, kernel)
        
        # 结合原图像
        enhanced = cv2.bitwise_or(binary_image, gradient)
        
        return enhanced
    
    @staticmethod
    def estimate_character_width(binary_image: np.ndarray, 
                               expected_char_count: int = 7) -> int:
        """
        估计字符宽度
        
        Args:
            binary_image (np.ndarray): 二值化图像
            expected_char_count (int): 期望字符数量
            
        Returns:
            int: 估计的字符宽度
        """
        # 计算垂直投影
        projection = SegmentationUtils.calculate_vertical_projection(binary_image)
        
        # 找到有内容的区域
        non_zero_indices = np.where(projection > 0)[0]
        if len(non_zero_indices) == 0:
            return binary_image.shape[1] // expected_char_count
        
        # 计算有效宽度
        effective_width = non_zero_indices[-1] - non_zero_indices[0] + 1
        
        # 估计单个字符宽度
        estimated_width = effective_width // expected_char_count
        
        return max(10, estimated_width)  # 最小宽度为10像素
    
    @staticmethod
    def validate_character_region(region: Tuple[int, int], 
                                image_shape: Tuple[int, int],
                                min_width: int = 5,
                                max_width: int = 100,
                                min_aspect_ratio: float = 0.1,
                                max_aspect_ratio: float = 3.0) -> bool:
        """
        验证字符区域的有效性
        
        Args:
            region (Tuple[int, int]): 字符区域 (start_x, end_x)
            image_shape (Tuple[int, int]): 图像尺寸 (height, width)
            min_width (int): 最小宽度
            max_width (int): 最大宽度
            min_aspect_ratio (float): 最小宽高比
            max_aspect_ratio (float): 最大宽高比
            
        Returns:
            bool: 是否有效
        """
        start_x, end_x = region
        height, width = image_shape
        
        # 检查坐标有效性
        if start_x < 0 or end_x > width or start_x >= end_x:
            return False
        
        # 检查宽度
        char_width = end_x - start_x
        if char_width < min_width or char_width > max_width:
            return False
        
        # 检查宽高比
        aspect_ratio = char_width / height
        if aspect_ratio < min_aspect_ratio or aspect_ratio > max_aspect_ratio:
            return False
        
        return True
    
    @staticmethod
    def merge_overlapping_regions(regions: List[Tuple[int, int]], 
                                overlap_threshold: float = 0.3) -> List[Tuple[int, int]]:
        """
        合并重叠的区域
        
        Args:
            regions (List[Tuple[int, int]]): 区域列表
            overlap_threshold (float): 重叠阈值
            
        Returns:
            List[Tuple[int, int]]: 合并后的区域列表
        """
        if not regions:
            return []
        
        # 按起始位置排序
        sorted_regions = sorted(regions)
        merged_regions = [sorted_regions[0]]
        
        for current_start, current_end in sorted_regions[1:]:
            last_start, last_end = merged_regions[-1]
            
            # 计算重叠程度
            overlap_start = max(last_start, current_start)
            overlap_end = min(last_end, current_end)
            
            if overlap_start < overlap_end:
                # 有重叠
                overlap_length = overlap_end - overlap_start
                min_length = min(last_end - last_start, current_end - current_start)
                overlap_ratio = overlap_length / min_length
                
                if overlap_ratio > overlap_threshold:
                    # 合并区域
                    merged_regions[-1] = (last_start, max(last_end, current_end))
                else:
                    # 不合并
                    merged_regions.append((current_start, current_end))
            else:
                # 无重叠
                merged_regions.append((current_start, current_end))
        
        return merged_regions
    
    @staticmethod
    def visualize_segmentation_process(original_image: np.ndarray,
                                     binary_image: np.ndarray,
                                     projection: np.ndarray,
                                     char_regions: List[Tuple[int, int]],
                                     title: str = "字符分割过程") -> None:
        """
        可视化分割过程
        
        Args:
            original_image (np.ndarray): 原始图像
            binary_image (np.ndarray): 二值化图像
            projection (np.ndarray): 投影数组
            char_regions (List[Tuple[int, int]]): 字符区域列表
            title (str): 图表标题
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        
        # 原始图像
        if len(original_image.shape) == 3:
            axes[0, 0].imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
        else:
            axes[0, 0].imshow(original_image, cmap='gray')
        axes[0, 0].set_title('原始图像')
        axes[0, 0].axis('off')
        
        # 二值化图像
        axes[0, 1].imshow(binary_image, cmap='gray')
        axes[0, 1].set_title('二值化图像')
        axes[0, 1].axis('off')
        
        # 垂直投影
        axes[1, 0].plot(projection)
        axes[1, 0].set_title('垂直投影')
        axes[1, 0].set_xlabel('X坐标')
        axes[1, 0].set_ylabel('投影值')
        axes[1, 0].grid(True)
        
        # 分割结果
        result_image = original_image.copy()
        if len(result_image.shape) == 2:
            result_image = cv2.cvtColor(result_image, cv2.COLOR_GRAY2BGR)
        
        for i, (start_x, end_x) in enumerate(char_regions):
            cv2.rectangle(result_image, (start_x, 0), 
                         (end_x, result_image.shape[0]), (0, 255, 0), 2)
            cv2.putText(result_image, str(i), (start_x, 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        axes[1, 1].imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
        axes[1, 1].set_title(f'分割结果 ({len(char_regions)}个区域)')
        axes[1, 1].axis('off')
        
        plt.suptitle(title)
        plt.tight_layout()
        plt.show()
    
    @staticmethod
    def calculate_segmentation_metrics(predicted_regions: List[Tuple[int, int]],
                                     ground_truth_regions: List[Tuple[int, int]],
                                     iou_threshold: float = 0.5) -> Dict[str, float]:
        """
        计算分割评估指标
        
        Args:
            predicted_regions (List[Tuple[int, int]]): 预测的区域
            ground_truth_regions (List[Tuple[int, int]]): 真实区域
            iou_threshold (float): IoU阈值
            
        Returns:
            Dict[str, float]: 评估指标
        """
        if not ground_truth_regions:
            return {'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0}
        
        # 计算匹配
        matched_pred = set()
        matched_gt = set()
        
        for i, pred_region in enumerate(predicted_regions):
            for j, gt_region in enumerate(ground_truth_regions):
                if j in matched_gt:
                    continue
                
                # 计算IoU
                iou = SegmentationUtils._calculate_region_iou(pred_region, gt_region)
                
                if iou >= iou_threshold:
                    matched_pred.add(i)
                    matched_gt.add(j)
                    break
        
        # 计算指标
        true_positives = len(matched_pred)
        false_positives = len(predicted_regions) - true_positives
        false_negatives = len(ground_truth_regions) - len(matched_gt)
        
        precision = true_positives / len(predicted_regions) if predicted_regions else 0.0
        recall = true_positives / len(ground_truth_regions)
        f1_score = (2 * precision * recall / (precision + recall) 
                   if precision + recall > 0 else 0.0)
        
        return {
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'true_positives': true_positives,
            'false_positives': false_positives,
            'false_negatives': false_negatives
        }
    
    @staticmethod
    def _calculate_region_iou(region1: Tuple[int, int], 
                            region2: Tuple[int, int]) -> float:
        """
        计算两个区域的IoU
        
        Args:
            region1 (Tuple[int, int]): 区域1
            region2 (Tuple[int, int]): 区域2
            
        Returns:
            float: IoU值
        """
        start1, end1 = region1
        start2, end2 = region2
        
        # 计算交集
        intersection_start = max(start1, start2)
        intersection_end = min(end1, end2)
        intersection = max(0, intersection_end - intersection_start)
        
        # 计算并集
        union = (end1 - start1) + (end2 - start2) - intersection
        
        # 计算IoU
        return intersection / union if union > 0 else 0.0
