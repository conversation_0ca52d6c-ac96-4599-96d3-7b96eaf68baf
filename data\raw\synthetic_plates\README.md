# 合成车牌数据集

## 数据集信息
- **类型**: synthetic_license_plates
- **版本**: 2.0
- **样本数量**: 100
- **生成日期**: 2025-07-30T11:14:27.237583

## 车牌类型
- 普通车牌 (7位): 省份 + 城市代码 + 5位字符
- 新能源车牌 (8位): 省份 + 城市代码 + 6位字符

## 支持的车牌颜色
blue, yellow, green, white

## 字符类别统计
- 省份字符: 31 个
- 字母: 24 个
- 数字: 10 个
- 总计: 65 个

## 数据增强特性
- rotation
- brightness
- contrast
- noise
- blur
- perspective_transform
- lighting_effects
- dirt_effects

## 目录结构
- `images/`: 主要训练图像
- `annotations/`: JSON格式标注文件
- `plates_only/`: 纯车牌图像（无背景）
- `scenes/`: 场景图像（包含背景）

## 使用说明
1. 图像文件格式: JPEG
2. 标注文件格式: JSON
3. 每个图像都有对应的标注文件
4. 标注包含车牌号码、字符位置、颜色等信息

## 注意事项
- 本数据集为合成数据，用于模型训练和测试
- 实际应用时建议结合真实数据进行训练
- 字符位置信息可用于字符分割和识别任务
