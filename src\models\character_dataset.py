#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字符数据集
Character Dataset

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import torch
from torch.utils.data import Dataset, DataLoader
import cv2
import numpy as np
from typing import Dict, Any, List, Tuple, Optional, Callable
from pathlib import Path
import json
import random
from PIL import Image
import torchvision.transforms as transforms

from ..utils.logger import LoggerMixin


class CharacterDataset(Dataset, LoggerMixin):
    """字符数据集类"""
    
    def __init__(self, data_dir: str, char_mapping: Dict[str, int],
                 transform: Optional[Callable] = None, 
                 target_size: Tuple[int, int] = (64, 64)):
        """
        初始化字符数据集
        
        Args:
            data_dir (str): 数据目录路径
            char_mapping (Dict[str, int]): 字符到索引的映射
            transform (Optional[Callable]): 数据变换
            target_size (Tuple[int, int]): 目标图像尺寸
        """
        self.data_dir = Path(data_dir)
        self.char_mapping = char_mapping
        self.transform = transform
        self.target_size = target_size
        
        # 支持的图像格式
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        
        # 加载数据
        self.samples = self._load_samples()
        
        self.logger.info(f"字符数据集初始化完成，共 {len(self.samples)} 个样本")
    
    def _load_samples(self) -> List[Tuple[str, int]]:
        """
        加载样本数据
        
        Returns:
            List[Tuple[str, int]]: 样本列表 (图像路径, 标签)
        """
        samples = []
        
        # 遍历字符目录
        for char_dir in self.data_dir.iterdir():
            if not char_dir.is_dir():
                continue
            
            char_name = char_dir.name
            if char_name not in self.char_mapping:
                self.logger.warning(f"未知字符类别: {char_name}")
                continue
            
            label = self.char_mapping[char_name]
            
            # 加载该字符的所有图像
            for image_file in char_dir.iterdir():
                if image_file.suffix.lower() in self.image_extensions:
                    samples.append((str(image_file), label))
        
        self.logger.info(f"加载了 {len(samples)} 个样本")
        return samples
    
    def __len__(self) -> int:
        """返回数据集大小"""
        return len(self.samples)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, int]:
        """
        获取单个样本
        
        Args:
            idx (int): 样本索引
            
        Returns:
            Tuple[torch.Tensor, int]: 图像张量和标签
        """
        image_path, label = self.samples[idx]
        
        # 加载图像
        image = self._load_image(image_path)
        
        # 应用变换
        if self.transform:
            image = self.transform(image)
        
        return image, label
    
    def _load_image(self, image_path: str) -> torch.Tensor:
        """
        加载图像
        
        Args:
            image_path (str): 图像路径
            
        Returns:
            torch.Tensor: 图像张量
        """
        try:
            # 使用PIL加载图像
            image = Image.open(image_path).convert('RGB')
            
            # 调整尺寸
            image = image.resize(self.target_size, Image.LANCZOS)
            
            # 转换为张量
            image = transforms.ToTensor()(image)
            
            return image
            
        except Exception as e:
            self.logger.error(f"加载图像失败 {image_path}: {str(e)}")
            # 返回空白图像
            return torch.zeros(3, self.target_size[1], self.target_size[0])
    
    def get_class_weights(self) -> torch.Tensor:
        """
        计算类别权重（用于处理类别不平衡）
        
        Returns:
            torch.Tensor: 类别权重
        """
        # 统计每个类别的样本数
        class_counts = {}
        for _, label in self.samples:
            class_counts[label] = class_counts.get(label, 0) + 1
        
        # 计算权重
        total_samples = len(self.samples)
        num_classes = len(self.char_mapping)
        
        weights = torch.zeros(num_classes)
        for class_id, count in class_counts.items():
            weights[class_id] = total_samples / (num_classes * count)
        
        return weights
    
    def get_class_distribution(self) -> Dict[str, int]:
        """
        获取类别分布
        
        Returns:
            Dict[str, int]: 类别分布
        """
        distribution = {}
        
        # 创建反向映射
        reverse_mapping = {v: k for k, v in self.char_mapping.items()}
        
        # 统计分布
        for _, label in self.samples:
            char_name = reverse_mapping.get(label, f'unknown_{label}')
            distribution[char_name] = distribution.get(char_name, 0) + 1
        
        return distribution


class SequenceCharacterDataset(Dataset, LoggerMixin):
    """序列字符数据集（用于CRNN训练）"""
    
    def __init__(self, data_dir: str, char_mapping: Dict[str, int],
                 transform: Optional[Callable] = None,
                 target_size: Tuple[int, int] = (32, 128),
                 max_sequence_length: int = 8):
        """
        初始化序列字符数据集
        
        Args:
            data_dir (str): 数据目录路径
            char_mapping (Dict[str, int]): 字符到索引的映射
            transform (Optional[Callable]): 数据变换
            target_size (Tuple[int, int]): 目标图像尺寸
            max_sequence_length (int): 最大序列长度
        """
        self.data_dir = Path(data_dir)
        self.char_mapping = char_mapping
        self.transform = transform
        self.target_size = target_size
        self.max_sequence_length = max_sequence_length
        
        # 特殊字符
        self.blank_token = 0  # CTC空白字符
        self.pad_token = len(char_mapping) + 1  # 填充字符
        
        # 加载数据
        self.samples = self._load_sequence_samples()
        
        self.logger.info(f"序列字符数据集初始化完成，共 {len(self.samples)} 个样本")
    
    def _load_sequence_samples(self) -> List[Tuple[str, str]]:
        """
        加载序列样本数据
        
        Returns:
            List[Tuple[str, str]]: 样本列表 (图像路径, 标签文本)
        """
        samples = []
        
        # 查找标注文件
        annotation_file = self.data_dir / 'annotations.json'
        if annotation_file.exists():
            # 从JSON文件加载
            with open(annotation_file, 'r', encoding='utf-8') as f:
                annotations = json.load(f)
            
            for item in annotations:
                image_path = self.data_dir / item['image']
                if image_path.exists():
                    samples.append((str(image_path), item['text']))
        else:
            # 从文件名推断标签
            for image_file in self.data_dir.glob('*.png'):
                # 假设文件名格式为: label_xxx.png
                filename = image_file.stem
                if '_' in filename:
                    label_text = filename.split('_')[0]
                    samples.append((str(image_file), label_text))
        
        return samples
    
    def __len__(self) -> int:
        """返回数据集大小"""
        return len(self.samples)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor, int]:
        """
        获取单个样本
        
        Args:
            idx (int): 样本索引
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor, int]: 图像张量、标签序列、序列长度
        """
        image_path, label_text = self.samples[idx]
        
        # 加载图像
        image = self._load_sequence_image(image_path)
        
        # 编码标签
        label_sequence, sequence_length = self._encode_label(label_text)
        
        # 应用变换
        if self.transform:
            image = self.transform(image)
        
        return image, label_sequence, sequence_length
    
    def _load_sequence_image(self, image_path: str) -> torch.Tensor:
        """
        加载序列图像
        
        Args:
            image_path (str): 图像路径
            
        Returns:
            torch.Tensor: 图像张量
        """
        try:
            # 使用PIL加载图像
            image = Image.open(image_path).convert('RGB')
            
            # 调整尺寸
            image = image.resize(self.target_size, Image.LANCZOS)
            
            # 转换为张量
            image = transforms.ToTensor()(image)
            
            return image
            
        except Exception as e:
            self.logger.error(f"加载序列图像失败 {image_path}: {str(e)}")
            # 返回空白图像
            return torch.zeros(3, self.target_size[0], self.target_size[1])
    
    def _encode_label(self, label_text: str) -> Tuple[torch.Tensor, int]:
        """
        编码标签文本
        
        Args:
            label_text (str): 标签文本
            
        Returns:
            Tuple[torch.Tensor, int]: 编码后的标签序列和长度
        """
        # 将文本转换为索引序列
        indices = []
        for char in label_text:
            if char in self.char_mapping:
                indices.append(self.char_mapping[char])
            else:
                # 未知字符用空白字符代替
                indices.append(self.blank_token)
        
        # 截断或填充到固定长度
        sequence_length = min(len(indices), self.max_sequence_length)
        
        if len(indices) > self.max_sequence_length:
            indices = indices[:self.max_sequence_length]
        else:
            indices.extend([self.pad_token] * (self.max_sequence_length - len(indices)))
        
        return torch.tensor(indices, dtype=torch.long), sequence_length


def create_character_transforms(training: bool = True, 
                              target_size: Tuple[int, int] = (64, 64)) -> transforms.Compose:
    """
    创建字符数据变换
    
    Args:
        training (bool): 是否为训练模式
        target_size (Tuple[int, int]): 目标尺寸
        
    Returns:
        transforms.Compose: 数据变换组合
    """
    if training:
        # 训练时的数据增强
        transform_list = [
            transforms.Resize(target_size),
            transforms.RandomRotation(degrees=5),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2),
            transforms.RandomAffine(degrees=0, translate=(0.1, 0.1), scale=(0.9, 1.1)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ]
    else:
        # 验证/测试时的变换
        transform_list = [
            transforms.Resize(target_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ]
    
    return transforms.Compose(transform_list)


def create_data_loaders(train_dataset: Dataset, val_dataset: Dataset,
                       batch_size: int = 32, num_workers: int = 4) -> Tuple[DataLoader, DataLoader]:
    """
    创建数据加载器
    
    Args:
        train_dataset (Dataset): 训练数据集
        val_dataset (Dataset): 验证数据集
        batch_size (int): 批次大小
        num_workers (int): 工作进程数
        
    Returns:
        Tuple[DataLoader, DataLoader]: 训练和验证数据加载器
    """
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=False
    )
    
    return train_loader, val_loader


def collate_sequence_batch(batch: List[Tuple[torch.Tensor, torch.Tensor, int]]) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
    """
    序列数据的批次整理函数
    
    Args:
        batch (List[Tuple[torch.Tensor, torch.Tensor, int]]): 批次数据
        
    Returns:
        Tuple[torch.Tensor, torch.Tensor, torch.Tensor]: 图像、标签、长度
    """
    images, labels, lengths = zip(*batch)
    
    # 堆叠图像
    images = torch.stack(images, dim=0)
    
    # 堆叠标签
    labels = torch.stack(labels, dim=0)
    
    # 转换长度
    lengths = torch.tensor(lengths, dtype=torch.long)
    
    return images, labels, lengths


class CharacterDatasetAnalyzer(LoggerMixin):
    """字符数据集分析器"""
    
    def __init__(self, dataset: CharacterDataset):
        """
        初始化分析器
        
        Args:
            dataset (CharacterDataset): 字符数据集
        """
        self.dataset = dataset
    
    def analyze_dataset(self) -> Dict[str, Any]:
        """
        分析数据集
        
        Returns:
            Dict[str, Any]: 分析结果
        """
        analysis = {
            'total_samples': len(self.dataset),
            'num_classes': len(self.dataset.char_mapping),
            'class_distribution': self.dataset.get_class_distribution(),
            'image_statistics': self._analyze_images()
        }
        
        self.logger.info(f"数据集分析完成: {analysis['total_samples']} 样本, {analysis['num_classes']} 类别")
        
        return analysis
    
    def _analyze_images(self) -> Dict[str, Any]:
        """
        分析图像统计信息
        
        Returns:
            Dict[str, Any]: 图像统计信息
        """
        # 随机采样分析
        sample_size = min(1000, len(self.dataset))
        indices = random.sample(range(len(self.dataset)), sample_size)
        
        widths, heights, channels = [], [], []
        
        for idx in indices:
            image, _ = self.dataset[idx]
            c, h, w = image.shape
            channels.append(c)
            heights.append(h)
            widths.append(w)
        
        return {
            'sample_size': sample_size,
            'avg_width': np.mean(widths),
            'avg_height': np.mean(heights),
            'avg_channels': np.mean(channels),
            'width_std': np.std(widths),
            'height_std': np.std(heights)
        }
    
    def plot_class_distribution(self, save_path: Optional[str] = None) -> None:
        """
        绘制类别分布图
        
        Args:
            save_path (Optional[str]): 保存路径
        """
        import matplotlib.pyplot as plt
        
        distribution = self.dataset.get_class_distribution()
        
        # 排序
        sorted_items = sorted(distribution.items(), key=lambda x: x[1], reverse=True)
        classes, counts = zip(*sorted_items)
        
        plt.figure(figsize=(12, 6))
        plt.bar(range(len(classes)), counts)
        plt.xlabel('字符类别')
        plt.ylabel('样本数量')
        plt.title('字符类别分布')
        plt.xticks(range(len(classes)), classes, rotation=45)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"类别分布图已保存: {save_path}")
        
        plt.show()
