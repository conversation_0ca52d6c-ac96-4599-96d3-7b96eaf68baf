#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据清洗器
Data Cleaner

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import cv2
import numpy as np
import hashlib
from pathlib import Path
from typing import Dict, Any, List, Set, Tuple
import json
import shutil
from collections import defaultdict
from tqdm import tqdm

from ..utils.logger import LoggerMixin
from .image_processor import ImageProcessor


class DataCleaner(LoggerMixin):
    """数据清洗器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化清洗器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.data_config = config.get('data', {})
        
        self.raw_data_path = Path(self.data_config.get('raw_data_path', 'data/raw'))
        self.processed_data_path = Path(self.data_config.get('processed_data_path', 'data/processed'))
        
        # 创建处理后数据目录
        self.processed_data_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化图像处理器
        self.image_processor = ImageProcessor(config)
        
        # 用于去重的哈希集合
        self.image_hashes: Set[str] = set()
        
        # 清洗统计信息
        self.cleaning_stats = {
            'total_files': 0,
            'valid_files': 0,
            'invalid_files': 0,
            'duplicate_files': 0,
            'processed_files': 0,
            'errors': []
        }
        
        self.logger.info("数据清洗器初始化完成")
    
    def clean_all_data(self) -> Dict[str, Any]:
        """
        清洗所有原始数据
        
        Returns:
            Dict[str, Any]: 清洗统计结果
        """
        self.logger.info("开始数据清洗")
        
        # 重置统计信息
        self.cleaning_stats = {
            'total_files': 0,
            'valid_files': 0,
            'invalid_files': 0,
            'duplicate_files': 0,
            'processed_files': 0,
            'errors': []
        }
        self.image_hashes.clear()
        
        # 遍历所有原始数据目录
        for data_source_dir in self.raw_data_path.iterdir():
            if data_source_dir.is_dir():
                self.logger.info(f"清洗数据源: {data_source_dir.name}")
                self._clean_data_source(data_source_dir)
        
        # 保存清洗报告
        self._save_cleaning_report()
        
        self.logger.info("数据清洗完成")
        self.logger.info(f"总文件数: {self.cleaning_stats['total_files']}")
        self.logger.info(f"有效文件: {self.cleaning_stats['valid_files']}")
        self.logger.info(f"无效文件: {self.cleaning_stats['invalid_files']}")
        self.logger.info(f"重复文件: {self.cleaning_stats['duplicate_files']}")
        self.logger.info(f"处理成功: {self.cleaning_stats['processed_files']}")
        
        return self.cleaning_stats
    
    def _clean_data_source(self, source_dir: Path) -> None:
        """
        清洗单个数据源
        
        Args:
            source_dir (Path): 数据源目录
        """
        # 创建对应的处理后目录
        processed_source_dir = self.processed_data_path / source_dir.name
        processed_source_dir.mkdir(exist_ok=True)
        
        # 获取所有图像文件
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.webp']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(list(source_dir.rglob(ext)))
            image_files.extend(list(source_dir.rglob(ext.upper())))
        
        self.logger.info(f"找到 {len(image_files)} 个图像文件")
        
        # 处理每个图像文件
        for img_file in tqdm(image_files, desc=f"处理 {source_dir.name}"):
            self.cleaning_stats['total_files'] += 1
            
            try:
                # 读取图像
                image = cv2.imread(str(img_file))
                
                if image is None:
                    self.cleaning_stats['invalid_files'] += 1
                    self.cleaning_stats['errors'].append(f"无法读取图像: {img_file}")
                    continue
                
                # 验证图像质量
                is_valid, error_msg = self.image_processor.validate_image(image)
                if not is_valid:
                    self.cleaning_stats['invalid_files'] += 1
                    self.cleaning_stats['errors'].append(f"{img_file}: {error_msg}")
                    continue
                
                # 检查重复
                image_hash = self._calculate_image_hash(image)
                if image_hash in self.image_hashes:
                    self.cleaning_stats['duplicate_files'] += 1
                    continue
                
                self.image_hashes.add(image_hash)
                self.cleaning_stats['valid_files'] += 1
                
                # 处理图像
                processed_image = self._process_image(image)
                
                # 保存处理后的图像
                output_filename = f"{image_hash}.jpg"
                output_path = processed_source_dir / output_filename
                
                success = cv2.imwrite(str(output_path), processed_image)
                if success:
                    self.cleaning_stats['processed_files'] += 1
                    
                    # 如果有对应的标注文件，也复制过来
                    self._copy_annotation_if_exists(img_file, output_path, image_hash)
                else:
                    self.cleaning_stats['errors'].append(f"保存图像失败: {output_path}")
                
            except Exception as e:
                self.cleaning_stats['invalid_files'] += 1
                self.cleaning_stats['errors'].append(f"处理图像出错 {img_file}: {str(e)}")
    
    def _process_image(self, image: np.ndarray) -> np.ndarray:
        """
        处理单张图像
        
        Args:
            image (np.ndarray): 输入图像
            
        Returns:
            np.ndarray: 处理后的图像
        """
        # 增强图像质量
        enhanced = self.image_processor.enhance_image_quality(image)
        
        # 尝试透视矫正
        corrected, success = self.image_processor.detect_and_correct_perspective(enhanced)
        if not success:
            corrected = enhanced
        
        # 标准化尺寸
        normalized = self.image_processor.normalize_image_size(corrected)
        
        return normalized
    
    def _calculate_image_hash(self, image: np.ndarray) -> str:
        """
        计算图像哈希值用于去重
        
        Args:
            image (np.ndarray): 输入图像
            
        Returns:
            str: 图像哈希值
        """
        # 使用感知哈希算法
        # 缩放到小尺寸
        small = cv2.resize(image, (8, 8), interpolation=cv2.INTER_AREA)
        
        # 转换为灰度
        gray = cv2.cvtColor(small, cv2.COLOR_BGR2GRAY)
        
        # 计算平均值
        avg = gray.mean()
        
        # 生成哈希
        hash_bits = []
        for pixel in gray.flatten():
            hash_bits.append('1' if pixel > avg else '0')
        
        hash_string = ''.join(hash_bits)
        
        # 转换为十六进制
        hash_int = int(hash_string, 2)
        return format(hash_int, '016x')
    
    def _copy_annotation_if_exists(self, original_path: Path, new_path: Path, image_hash: str) -> None:
        """
        如果存在标注文件，复制到新位置
        
        Args:
            original_path (Path): 原始图像路径
            new_path (Path): 新图像路径
            image_hash (str): 图像哈希值
        """
        # 查找可能的标注文件
        annotation_extensions = ['.json', '.xml', '.txt']
        
        for ext in annotation_extensions:
            annotation_path = original_path.with_suffix(ext)
            if annotation_path.exists():
                # 复制标注文件
                new_annotation_path = new_path.with_suffix(ext)
                try:
                    shutil.copy2(annotation_path, new_annotation_path)
                    
                    # 如果是JSON文件，更新其中的文件名引用
                    if ext == '.json':
                        self._update_json_annotation(new_annotation_path, new_path.name)
                        
                except Exception as e:
                    self.cleaning_stats['errors'].append(f"复制标注文件失败 {annotation_path}: {str(e)}")
                break
    
    def _update_json_annotation(self, annotation_path: Path, new_filename: str) -> None:
        """
        更新JSON标注文件中的文件名引用
        
        Args:
            annotation_path (Path): 标注文件路径
            new_filename (str): 新的图像文件名
        """
        try:
            with open(annotation_path, 'r', encoding='utf-8') as f:
                annotation = json.load(f)
            
            # 更新文件名字段
            if 'image_filename' in annotation:
                annotation['image_filename'] = new_filename
            if 'filename' in annotation:
                annotation['filename'] = new_filename
            
            with open(annotation_path, 'w', encoding='utf-8') as f:
                json.dump(annotation, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.warning(f"更新JSON标注失败 {annotation_path}: {str(e)}")
    
    def _save_cleaning_report(self) -> None:
        """保存清洗报告"""
        report = {
            'cleaning_summary': self.cleaning_stats,
            'data_sources': {},
            'recommendations': []
        }
        
        # 统计各数据源的情况
        for source_dir in self.processed_data_path.iterdir():
            if source_dir.is_dir():
                image_count = len(list(source_dir.glob('*.jpg')))
                report['data_sources'][source_dir.name] = {
                    'processed_images': image_count
                }
        
        # 生成建议
        if self.cleaning_stats['invalid_files'] > self.cleaning_stats['valid_files'] * 0.1:
            report['recommendations'].append("无效文件比例较高，建议检查数据采集质量")
        
        if self.cleaning_stats['duplicate_files'] > self.cleaning_stats['valid_files'] * 0.2:
            report['recommendations'].append("重复文件较多，建议优化数据采集去重机制")
        
        # 保存报告
        report_path = self.processed_data_path / 'cleaning_report.json'
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"清洗报告已保存: {report_path}")
    
    def remove_low_quality_images(self, quality_threshold: float = 100.0) -> int:
        """
        移除低质量图像
        
        Args:
            quality_threshold (float): 质量阈值（拉普拉斯方差）
            
        Returns:
            int: 移除的图像数量
        """
        removed_count = 0
        
        for source_dir in self.processed_data_path.iterdir():
            if source_dir.is_dir():
                for img_file in source_dir.glob('*.jpg'):
                    try:
                        image = cv2.imread(str(img_file))
                        if image is not None:
                            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
                            
                            if laplacian_var < quality_threshold:
                                img_file.unlink()  # 删除文件
                                
                                # 同时删除对应的标注文件
                                for ext in ['.json', '.xml', '.txt']:
                                    ann_file = img_file.with_suffix(ext)
                                    if ann_file.exists():
                                        ann_file.unlink()
                                
                                removed_count += 1
                                
                    except Exception as e:
                        self.logger.warning(f"检查图像质量失败 {img_file}: {str(e)}")
        
        self.logger.info(f"移除了 {removed_count} 张低质量图像")
        return removed_count
